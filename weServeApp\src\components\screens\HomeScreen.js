import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import Card from '../common/Card';
import Button from '../common/Button';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { SERVICE_CATEGORIES } from '../../constants/categories';
import { useAuthStore } from '../../stores/authStore';
import { useServicesStore } from '../../stores/servicesStore';
import servicesService from '../../services/servicesService';

const { width } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  
  const { user, userRole } = useAuthStore();
  const { services, serviceRequests, isLoading, setLoading } = useServicesStore();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      if (userRole === 'client') {
        // Pour les clients : charger leurs demandes de service
        const result = await servicesService.getMyServiceRequests();
        if (result.success) {
          // Les données sont déjà dans le store via ServicesListScreen
          console.log('✅ Demandes chargées pour le client');
        }
      } else {
        // Pour les prestataires : charger les demandes ouvertes
        const result = await servicesService.getOpenServiceRequests();
        if (result.success) {
          // Les données sont déjà dans le store via ServicesListScreen
          console.log('✅ Demandes ouvertes chargées pour le prestataire');
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleCategoryPress = (category) => {
    setSelectedCategory(selectedCategory?.id === category.id ? null : category);
    // Navigation vers la liste des services filtrés
            navigation.navigate('Services', { category: category.id });
  };

  const handleCreateService = () => {
    if (userRole === 'client') {
      navigation.navigate('CreateServiceRequest');
    } else {
      navigation.navigate('CreateProviderService');
    }
  };

  const renderWelcomeHeader = () => (
    <LinearGradient
      colors={[COLORS.primary, COLORS.secondary]}
      style={styles.welcomeHeader}
    >
      <View style={styles.welcomeContent}>
        <View style={styles.welcomeText}>
          <Text style={styles.welcomeTitle}>
            Bonjour, {user?.firstName || 'Utilisateur'} !
          </Text>
          <Text style={styles.welcomeSubtitle}>
            {userRole === 'client' 
              ? 'Que souhaitez-vous faire réparer aujourd\'hui ?'
              : 'Prêt à aider vos clients ?'
            }
          </Text>
        </View>
        <View style={styles.avatarContainer}>
          <Ionicons 
            name={userRole === 'client' ? 'person' : 'construct'} 
            size={40} 
            color={COLORS.white} 
          />
        </View>
      </View>
    </LinearGradient>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <Text style={styles.sectionTitle}>Actions rapides</Text>
      <View style={styles.actionsGrid}>
        <TouchableOpacity
          style={styles.actionCard}
          onPress={handleCreateService}
        >
          <LinearGradient
            colors={[COLORS.accent, COLORS.warning]}
            style={styles.actionIcon}
          >
            <Ionicons 
              name={userRole === 'client' ? 'add-circle' : 'construct'} 
              size={30} 
              color={COLORS.white} 
            />
          </LinearGradient>
          <Text style={styles.actionText}>
            {userRole === 'client' ? 'Nouvelle demande' : 'Créer un service'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => navigation.navigate('Services')}
        >
          <LinearGradient
            colors={[COLORS.secondary, COLORS.success]}
            style={styles.actionIcon}
          >
            <Ionicons name="search" size={30} color={COLORS.white} />
          </LinearGradient>
          <Text style={styles.actionText}>
            {userRole === 'client' ? 'Rechercher' : 'Mes services'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => navigation.navigate('Dashboard')}
        >
          <LinearGradient
            colors={[COLORS.primary, COLORS.accent]}
            style={styles.actionIcon}
          >
            <Ionicons name="stats-chart" size={30} color={COLORS.white} />
          </LinearGradient>
          <Text style={styles.actionText}>Tableau de bord</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => navigation.navigate('Profile')}
        >
          <LinearGradient
            colors={[COLORS.gray, COLORS.dark]}
            style={styles.actionIcon}
          >
            <Ionicons name="person" size={30} color={COLORS.white} />
          </LinearGradient>
          <Text style={styles.actionText}>Profil</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCategories = () => (
    <View style={styles.categories}>
      <Text style={styles.sectionTitle}>Catégories de services</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesScroll}
      >
        {SERVICE_CATEGORIES.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryCard,
              selectedCategory?.id === category.id && styles.categoryCardSelected
            ]}
            onPress={() => handleCategoryPress(category)}
          >
            <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
              <Ionicons name={category.icon} size={24} color={COLORS.white} />
            </View>
            <Text style={[
              styles.categoryText,
              selectedCategory?.id === category.id && styles.categoryTextSelected
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderRecentActivity = () => (
    <View style={styles.recentActivity}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Activité récente</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Dashboard')}>
          <Text style={styles.seeAllText}>Voir tout</Text>
        </TouchableOpacity>
      </View>
      
      {userRole === 'client' ? (
        // Pour les clients : demandes récentes
        <View>
          {serviceRequests.slice(0, 3).map((request, index) => (
            <Card key={index} style={styles.activityCard}>
              <View style={styles.activityContent}>
                <View style={styles.activityIcon}>
                  <Ionicons name="document-text" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.activityText}>
                  <Text style={styles.activityTitle}>{request.title}</Text>
                  <Text style={styles.activitySubtitle}>
                    Statut: {request.status}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
              </View>
            </Card>
          ))}
        </View>
      ) : (
        // Pour les prestataires : services récents
        <View>
          {services.slice(0, 3).map((service, index) => (
            <Card key={index} style={styles.activityCard}>
              <View style={styles.activityContent}>
                <View style={styles.activityIcon}>
                  <Ionicons name="construct" size={20} color={COLORS.secondary} />
                </View>
                <View style={styles.activityText}>
                  <Text style={styles.activityTitle}>{service.title}</Text>
                  <Text style={styles.activitySubtitle}>
                    {service.price}€/h
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
              </View>
            </Card>
          ))}
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderWelcomeHeader()}
        {renderQuickActions()}
        {renderCategories()}
        {renderRecentActivity()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SIZES.extraLarge,
  },
  welcomeHeader: {
    padding: SIZES.padding * 1.5,
    marginBottom: SIZES.extraLarge,
  },
  welcomeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  welcomeText: {
    flex: 1,
  },
  welcomeTitle: {
    fontSize: 24,
    fontFamily: FONTS.bold,
    color: COLORS.white,
    marginBottom: SIZES.base,
  },
  welcomeSubtitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.regular,
    color: COLORS.white,
    opacity: 0.9,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickActions: {
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.extraLarge,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: SIZES.medium,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - SIZES.padding * 3) / 2,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    alignItems: 'center',
    marginBottom: SIZES.medium,
    ...SHADOWS.small,
  },
  actionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.medium,
  },
  actionText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    textAlign: 'center',
  },
  categories: {
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.extraLarge,
  },
  categoriesScroll: {
    paddingRight: SIZES.padding,
  },
  categoryCard: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    alignItems: 'center',
    marginRight: SIZES.medium,
    minWidth: 100,
    ...SHADOWS.small,
  },
  categoryCardSelected: {
    backgroundColor: COLORS.primary,
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.base,
  },
  categoryText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    textAlign: 'center',
  },
  categoryTextSelected: {
    color: COLORS.white,
  },
  recentActivity: {
    paddingHorizontal: SIZES.padding,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.medium,
  },
  seeAllText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  activityCard: {
    marginBottom: SIZES.medium,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SIZES.medium,
  },
  activityText: {
    flex: 1,
  },
  activityTitle: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: SIZES.base,
  },
  activitySubtitle: {
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
});

export default HomeScreen;
