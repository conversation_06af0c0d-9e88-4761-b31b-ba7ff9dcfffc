import apiService from './apiService';

class ServicesService {
  // === DEMANDES DE SERVICE (CLIENTS) ===
  
  // Créer une nouvelle demande de service
  async createServiceRequest(requestData, photos = []) {
    try {
      if (photos.length > 0) {
        // Upload avec photos via FormData
        const formData = new FormData();
        
        // Ajout des données de la demande
        Object.keys(requestData).forEach(key => {
          if (key === 'location' || key === 'availability') {
            formData.append(key, JSON.stringify(requestData[key]));
          } else {
            formData.append(key, requestData[key]);
          }
        });
        
        // Ajout des photos
        photos.forEach((photo, index) => {
          formData.append('photos', {
            uri: photo.uri,
            type: photo.type || 'image/jpeg',
            name: `photo_${index}.jpg`
          });
        });
        
        return await apiService.upload('/services', formData);
      } else {
        // Création sans photos
        return await apiService.post('/services', requestData);
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la création de la demande',
        error
      };
    }
  }

  // Récupérer les demandes de service ouvertes (pour prestataires)
  async getOpenServiceRequests(filters = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.location) queryParams.append('location', filters.location);
      if (filters.budget) queryParams.append('budget', filters.budget);
      
      const endpoint = filters && Object.keys(filters).length > 0 
        ? `/services?${queryParams.toString()}`
        : '/services';
        
      const response = await apiService.get(endpoint);
      
      // Le backend renvoie directement un tableau ou { data: [...] }
      if (response && Array.isArray(response)) {
        return { success: true, data: response };
      } else if (response && response.data && Array.isArray(response.data)) {
        return { success: true, data: response.data };
      } else {
        console.warn('Format de réponse inattendu:', response);
        return { success: false, message: 'Format de réponse invalide' };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération des demandes',
        error
      };
    }
  }

  // Récupérer toutes les demandes de service (pour filtrage côté client)
  async getAllServiceRequests(filters = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.location) queryParams.append('location', filters.location);
      if (filters.budget) queryParams.append('budget', filters.budget);
      
      const endpoint = filters && Object.keys(filters).length > 0 
        ? `/services?${queryParams.toString()}`
        : '/services';
        
      const response = await apiService.get(endpoint);
      
      // Le backend renvoie directement un tableau ou { data: [...] }
      if (response && Array.isArray(response)) {
        return { success: true, data: response };
      } else if (response && response.data && Array.isArray(response.data)) {
        return { success: true, data: response.data };
      } else {
        console.warn('Format de réponse inattendu:', response);
        return { success: false, message: 'Format de réponse invalide' };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération de toutes les demandes',
        error
      };
    }
  }

  // Récupérer mes demandes de service (pour clients)
  async getMyServiceRequests(filters = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.from) queryParams.append('from', filters.from);
      if (filters.to) queryParams.append('to', filters.to);
      if (filters.category) queryParams.append('category', filters.category);
      
      const endpoint = filters && Object.keys(filters).length > 0 
        ? `/services/my-services?${queryParams.toString()}`
        : '/services/my-services';
        
      const response = await apiService.get(endpoint);
      
      // Le backend renvoie directement un tableau ou { data: [...] }
      if (response && Array.isArray(response)) {
        return { success: true, data: response };
      } else if (response && response.data && Array.isArray(response.data)) {
        return { success: true, data: response.data };
      } else {
        console.warn('Format de réponse inattendu:', response);
        return { success: false, message: 'Format de réponse invalide' };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération de mes demandes',
        error
      };
    }
  }

  // Récupérer le détail d'une demande
  async getServiceRequest(id) {
    try {
      return await apiService.get(`/services/${id}`);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération de la demande',
        error
      };
    }
  }

  // Modifier une demande de service
  async updateServiceRequest(id, updates) {
    try {
      return await apiService.put(`/services/${id}`, updates);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la modification de la demande',
        error
      };
    }
  }

  // Supprimer une demande de service
  async deleteServiceRequest(id) {
    try {
      return await apiService.delete(`/services/${id}`);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la suppression de la demande',
        error
      };
    }
  }

  // Accepter une demande de service (pour prestataires)
  async acceptServiceRequest(id) {
    try {
      return await apiService.post(`/services/${id}/accept`);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de l\'acceptation de la demande',
        error
      };
    }
  }

  // Rejeter une demande de service (pour prestataires)
  async rejectServiceRequest(id, reason) {
    try {
      return await apiService.post(`/services/${id}/reject`, { reason });
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors du rejet de la demande',
        error
      };
    }
  }

  // Marquer une demande comme terminée
  async completeServiceRequest(id) {
    try {
      return await apiService.post(`/services/${id}/complete`);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la finalisation de la demande',
        error
      };
    }
  }

  // Récupérer les demandes à proximité
  async getNearbyServiceRequests(location, radius = 10) {
    try {
      const params = {
        latitude: location.latitude,
        longitude: location.longitude,
        radius
      };
      return await apiService.get('/services/nearby', params);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération des demandes à proximité',
        error
      };
    }
  }

  // === SERVICES DES PRESTATAIRES ===
  
  // Créer un nouveau service (prestataire)
  async createProviderService(serviceData, photos = []) {
    try {
      if (photos.length > 0) {
        // Upload avec photos via FormData
        const formData = new FormData();
        
        // Ajout des données du service
        Object.keys(serviceData).forEach(key => {
          if (key === 'location' || key === 'availability') {
            formData.append(key, JSON.stringify(serviceData[key]));
          } else {
            formData.append(key, serviceData[key]);
          }
        });
        
        // Ajout des photos
        photos.forEach((photo, index) => {
          formData.append('photos', {
            uri: photo.uri,
            type: photo.type || 'image/jpeg',
            name: `photo_${index}.jpg`
          });
        });
        
        return await apiService.upload('/provider-services', formData);
      } else {
        // Création sans photos
        return await apiService.post('/provider-services', serviceData);
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la création du service',
        error
      };
    }
  }

  // Récupérer tous les services des prestataires
  async getAllProviderServices(filters = {}) {
    try {
      const response = await apiService.get('/provider-services', filters);
      
      // Si la réponse contient des services avec seulement l'ID du prestataire,
      // on peut les traiter ici pour afficher "Prestataire" au lieu de "Prestataire inconnu"
      if (response && Array.isArray(response)) {
        return { success: true, data: response };
      } else if (response && response.data && Array.isArray(response.data)) {
        return { success: true, data: response.data };
      } else {
        console.warn('Format de réponse inattendu pour getAllProviderServices:', response);
        return { success: false, message: 'Format de réponse invalide' };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération des services',
        error
      };
    }
  }

  // Récupérer mes services (pour prestataires)
  async getMyProviderServices() {
    try {
      return await apiService.get('/provider-services/my-services');
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération de mes services',
        error
      };
    }
  }

  // Récupérer le détail d'un service
  async getProviderService(id) {
    try {
      return await apiService.get(`/provider-services/${id}`);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération du service',
        error
      };
    }
  }

  // Modifier un service
  async updateProviderService(id, serviceData) {
    try {
      return await apiService.put(`/provider-services/${id}`, serviceData);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la modification du service',
        error
      };
    }
  }

  // Supprimer un service
  async deleteProviderService(id) {
    try {
      return await apiService.delete(`/provider-services/${id}`);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la suppression du service',
        error
      };
    }
  }

  // === UTILITAIRES ===
  
  // Rechercher des services
  async searchServices(query, filters = {}) {
    try {
      const params = { q: query, ...filters };
      return await apiService.get('/services/search', params);
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la recherche',
        error
      };
    }
  }

  // Obtenir les catégories de services
  async getServiceCategories() {
    try {
      return await apiService.get('/categories');
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération des catégories',
        error
      };
    }
  }

  // Obtenir les statistiques des services
  async getServiceStats() {
    try {
      return await apiService.get('/services/stats');
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Erreur lors de la récupération des statistiques',
        error
      };
    }
  }
}

// Instance unique du service des services
const servicesService = new ServicesService();

export default servicesService;
