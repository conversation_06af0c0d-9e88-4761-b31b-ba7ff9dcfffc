// Utilitaires pour les filtres des demandes de service

// Créer un filtre de date pour les 30 derniers jours
export const createRecentFilter = (days = 30) => {
  const from = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
  return { from };
};

// Créer un filtre de date pour une période spécifique
export const createDateRangeFilter = (fromDate, toDate) => {
  return {
    from: fromDate.toISOString(),
    to: toDate.toISOString()
  };
};

// Créer un filtre de statut
export const createStatusFilter = (status) => {
  return { status };
};

// Créer un filtre de catégorie
export const createCategoryFilter = (category) => {
  return { category };
};

// Combiner plusieurs filtres
export const combineFilters = (...filters) => {
  return filters.reduce((combined, filter) => {
    if (filter && typeof filter === 'object') {
      return { ...combined, ...filter };
    }
    return combined;
  }, {});
};

// Filtres prédéfinis utiles
export const FILTER_PRESETS = {
  // Pour les clients
  CLIENT: {
    ALL: {},
    PENDING: { status: 'pending' },
    IN_PROGRESS: { status: 'in_progress' },
    COMPLETED: { status: 'completed' },
    RECENT_30_DAYS: createRecentFilter(30),
    RECENT_7_DAYS: createRecentFilter(7),
  },
  
  // Pour les prestataires
  PROVIDER: {
    ALL: {},
    OPEN: { status: 'pending' },
    IN_PROGRESS: { status: 'in_progress' },
    COMPLETED: { status: 'completed' },
    BY_CATEGORY: (category) => createCategoryFilter(category),
  }
};

// Fonction pour formater les filtres en paramètres d'URL
export const formatFiltersForAPI = (filters) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      queryParams.append(key, value);
    }
  });
  
  return queryParams.toString();
};

// Fonction pour valider les filtres
export const validateFilters = (filters) => {
  const validFilters = {};
  
  // Validation des dates
  if (filters.from) {
    const fromDate = new Date(filters.from);
    if (!isNaN(fromDate.getTime())) {
      validFilters.from = filters.from;
    }
  }
  
  if (filters.to) {
    const toDate = new Date(filters.to);
    if (!isNaN(toDate.getTime())) {
      validFilters.to = filters.to;
    }
  }
  
  // Validation du statut
  if (filters.status && ['pending', 'in_progress', 'completed', 'cancelled'].includes(filters.status)) {
    validFilters.status = filters.status;
  }
  
  // Validation de la catégorie
  if (filters.category && filters.category.trim()) {
    validFilters.category = filters.category.trim();
  }
  
  // Validation du budget
  if (filters.budget && !isNaN(Number(filters.budget))) {
    validFilters.budget = Number(filters.budget);
  }
  
  return validFilters;
};
