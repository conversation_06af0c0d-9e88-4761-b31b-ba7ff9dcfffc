import mongoose from 'mongoose';

const userSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, index: true, sparse: true },
    phone: { type: String, index: true, sparse: true },
    passwordHash: { type: String },
    avatarUrl: { type: String }, // Gardé pour compatibilité avec l'existant
    profilePhoto: { type: String }, // Nouveau champ pour la photo en base64
    role: { type: String, enum: ['client', 'provider'], default: 'client' },
    authProvider: { type: String, enum: ['local', 'google', 'facebook'], default: 'local' },
    authProviderId: { type: String },
    refreshToken: { type: String },
    resetPasswordToken: { type: String },
    resetPasswordExpires: { type: Date },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 }
  },
  { timestamps: true }
);

const User = mongoose.model('User', userSchema);
export default User; 