import { Platform } from 'react-native';

// Configuration pour désactiver les fonctionnalités problématiques sur Expo SDK 52
export const screenConfig = {
  // Désactiver enableScreens sur Android pour éviter l'erreur topInsetsChange
  enableScreens: Platform.OS === 'ios',
  
  // Configuration des transitions d'écran
  screenOptions: {
    headerShown: false,
    gestureEnabled: true,
    // Utiliser des transitions simples pour éviter les problèmes
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
  },
  
  // Configuration des onglets
  tabBarOptions: {
    activeTintColor: '#2563EB',
    inactiveTintColor: 'gray',
    style: {
      backgroundColor: '#ffffff',
      borderTopWidth: 1,
      borderTopColor: '#E5E7EB',
    },
  },
};

// Fonction pour initialiser la configuration des écrans
export const initializeScreens = () => {
  // Désactiver les fonctionnalités problématiques
  if (Platform.OS === 'android') {
    // Configuration spécifique pour Android
    return {
      ...screenConfig,
      enableScreens: false,
    };
  }
  
  return screenConfig;
};
