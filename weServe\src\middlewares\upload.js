import multer from 'multer';

// Configuration de Multer pour le stockage en mémoire
const storage = multer.memoryStorage();

// Configuration des limites
const limits = {
  files: 5, // Maximum 5 fichiers
  fileSize: 10 * 1024 * 1024 // 10MB par fichier
};

// Filtre pour les types de fichiers autorisés
const fileFilter = (req, file, cb) => {
  // Vérifier le type MIME
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Seuls les fichiers image sont autorisés'), false);
  }
};

// Configuration de Multer
const upload = multer({
  storage,
  limits,
  fileFilter
});

// Middleware pour les photos multiples
export const uploadPhotos = upload.array('photos', 5);

// Middleware pour une seule photo
export const uploadSinglePhoto = upload.single('photo');

// Middleware d'erreur pour Multer
export const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'Fichier trop volumineux. Taille maximum : 10MB'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Trop de fichiers. Maximum : 5 fichiers'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Champ de fichier inattendu'
      });
    }
  }
  
  if (error.message === 'Seuls les fichiers image sont autorisés') {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  next(error);
};

