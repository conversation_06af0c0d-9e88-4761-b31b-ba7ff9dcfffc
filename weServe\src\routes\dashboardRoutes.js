import { Router } from 'express';
import { authenticate } from '../middlewares/auth.js';
import { authorizeRoles } from '../middlewares/roles.js';
import { myRequests, providerCompleted } from '../controllers/dashboardController.js';

const router = Router();

router.get('/requests', authenticate, authorizeRoles('client'), myRequests);
router.get('/completed', authenticate, authorizeR<PERSON>s('provider'), providerCompleted);

export default router; 