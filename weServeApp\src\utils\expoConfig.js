import { Platform } from 'react-native';

// Configuration spécifique pour Expo SDK 52
export const expoConfig = {
  // Désactiver les fonctionnalités problématiques
  enableScreens: Platform.OS === 'ios',
  
  // Configuration des safe areas
  safeArea: {
    // Désactiver les animations problématiques
    enableAnimations: Platform.OS === 'ios',
    
    // Configuration des transitions
    transitions: {
      // Utiliser des transitions simples
      cardStyleInterpolator: ({ current, layouts }) => ({
        cardStyle: {
          transform: [
            {
              translateX: current.progress.interpolate({
                inputRange: [0, 1],
                outputRange: [layouts.screen.width, 0],
              }),
            },
          ],
        },
      }),
    },
  },
  
  // Configuration des écrans
  screens: {
    // Désactiver les fonctionnalités problématiques sur Android
    android: {
      enableScreens: false,
      enableAnimations: false,
      enableGestures: true,
    },
    
    // Configuration iOS
    ios: {
      enableScreens: true,
      enableAnimations: true,
      enableGestures: true,
    },
  },
  
  // Configuration de la navigation
  navigation: {
    // Désactiver les animations problématiques
    animationEnabled: Platform.OS === 'ios',
    
    // Configuration des gestes
    gestureEnabled: true,
    
    // Configuration des transitions
    transitionSpec: {
      open: {
        animation: 'timing',
        config: {
          duration: 300,
          easing: 'easeInOut',
        },
      },
      close: {
        animation: 'timing',
        config: {
          duration: 300,
          easing: 'easeInOut',
        },
      },
    },
  },
};

// Fonction pour obtenir la configuration selon la plateforme
export const getExpoConfig = (type = 'general') => {
  if (type === 'android') {
    return {
      ...expoConfig,
      ...expoConfig.screens.android,
    };
  }
  
  if (type === 'ios') {
    return {
      ...expoConfig,
      ...expoConfig.screens.ios,
    };
  }
  
  return expoConfig;
};

// Configuration pour éviter l'erreur topInsetsChange
export const topInsetsConfig = {
  // Désactiver les fonctionnalités problématiques
  enableScreens: Platform.OS === 'ios',
  
  // Configuration des safe areas
  safeArea: {
    // Désactiver les animations
    enableAnimations: false,
    
    // Configuration des transitions
    transitions: {
      // Utiliser des transitions simples
      cardStyleInterpolator: ({ current, layouts }) => ({
        cardStyle: {
          transform: [
            {
              translateX: current.progress.interpolate({
                inputRange: [0, 1],
                outputRange: [layouts.screen.width, 0],
              }),
            },
          ],
        },
      }),
    },
  },
};
