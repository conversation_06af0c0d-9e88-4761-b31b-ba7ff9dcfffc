import apiService from './apiService';
import { useAuthStore } from '../stores/authStore';

class AuthService {
  // Connexion utilisateur
  async login(credentials) {
    try {
      const response = await apiService.post('/auth/login', credentials);
      
      // Le backend renvoie l'objet utilisateur dans la propriété 'user'
      if (response && response.user && response.user.id) {
        // Mettre à jour le store avec les données utilisateur et le token
        const { login } = useAuthStore.getState();
        const token = response.tokens?.accessToken || null;
        login(response.user, token);
        return { success: true, user: response.user, token };
      } else {
        return { success: false, message: response.message || 'Échec de la connexion' };
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      return { 
        success: false, 
        message: error.message || 'Erreur de connexion. Veuillez réessayer.' 
      };
    }
  }

  // Inscription utilisateur
  async signup(userData) {
    try {
      const response = await apiService.post('/auth/signup', userData);
      
      // Le backend renvoie l'objet utilisateur dans la propriété 'user'
      if (response && response.user && response.user.id) {
        // Mettre à jour le store avec les données utilisateur et le token
        const { login } = useAuthStore.getState();
        const token = response.tokens?.accessToken || null;
        login(response.user, token);
        return { success: true, user: response.user, token };
      } else {
        return { success: false, message: response.message || 'Échec de l\'inscription' };
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      return { 
        success: false, 
        message: error.message || 'Erreur d\'inscription. Veuillez réessayer.' 
      };
    }
  }

  // Déconnexion utilisateur
  async logout() {
    try {
      // Appel API pour déconnecter côté serveur
      await apiService.post('/auth/logout');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      // Toujours déconnecter côté client
      const { logout } = useAuthStore.getState();
      logout();
    }
  }

  // Vérifier le statut d'authentification
  async checkAuthStatus() {
    try {
      const response = await apiService.get('/auth/me');
      
      // Le backend renvoie l'objet utilisateur dans la propriété 'user'
      if (response && response.user && response.user.id) {
        // Mettre à jour le store avec les données utilisateur et le token
        const { login } = useAuthStore.getState();
        const token = response.tokens?.accessToken || null;
        login(response.user, token);
        return { success: true, user: response.user, token };
      } else {
        // L'utilisateur n'est pas authentifié
        const { logout } = useAuthStore.getState();
        logout();
        return { success: false, message: 'Non authentifié' };
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du statut:', error);
      // En cas d'erreur, déconnecter l'utilisateur
      const { logout } = useAuthStore.getState();
      logout();
      return { success: false, message: 'Erreur de vérification' };
    }
  }

  // Rafraîchir le token
  async refreshToken() {
    try {
      const response = await apiService.post('/auth/refresh');
      
      // Le backend renvoie l'objet utilisateur dans la propriété 'user'
      if (response && response.user && response.user.id) {
        // Mettre à jour le token dans le store
        const { updateProfile } = useAuthStore.getState();
        updateProfile({ token: response.token });
        return { success: true, token: response.token };
      } else {
        return { success: false, message: response.message || 'Échec du rafraîchissement du token' };
      }
    } catch (error) {
      console.error('Erreur lors du rafraîchissement du token:', error);
      return { 
        success: false, 
        message: error.message || 'Erreur de rafraîchissement du token' 
      };
    }
  }
}

// Instance unique du service d'authentification
const authService = new AuthService();

export default authService;
