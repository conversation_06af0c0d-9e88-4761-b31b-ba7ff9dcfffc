import { Platform } from 'react-native';

// Fonction pour désactiver les fonctionnalités problématiques
export const disableProblematicFeatures = () => {
  // Désactiver les fonctionnalités problématiques sur Android
  if (Platform.OS === 'android') {
    // Désactiver react-native-screens
    try {
      // Forcer la désactivation des écrans natifs
      if (global.__turboModuleProxy) {
        // Désactiver les modules problématiques
        global.__turboModuleProxy = undefined;
      }
    } catch (error) {
      console.log('Désactivation des modules problématiques:', error.message);
    }
  }
  
  return true;
};

// Configuration pour désactiver les animations problématiques
export const disableProblematicAnimations = () => {
  return {
    // Désactiver toutes les animations
    animationEnabled: false,
    
    // Désactiver les transitions
    cardStyleInterpolator: () => ({
      cardStyle: {
        opacity: 1,
      },
    }),
    
    // Désactiver les gestes problématiques
    gestureEnabled: false,
    
    // Désactiver les safe areas problématiques
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 },
  };
};

// Configuration pour éviter l'erreur topInsetsChange
export const avoidTopInsetsError = () => {
  return {
    // Désactiver complètement les fonctionnalités problématiques
    enableScreens: false,
    
    // Configuration des transitions
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
    
    // Désactiver les animations
    animationEnabled: false,
    
    // Désactiver les gestes
    gestureEnabled: false,
    
    // Configuration des safe areas
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 },
  };
};

// Configuration complète pour éviter tous les problèmes
export const getSafeConfiguration = () => {
  return {
    // Désactiver les fonctionnalités problématiques
    enableScreens: false,
    
    // Configuration des transitions
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
    
    // Désactiver les animations
    animationEnabled: false,
    
    // Désactiver les gestes
    gestureEnabled: false,
    
    // Configuration des safe areas
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 },
    
    // Configuration des onglets
    tabBarOptions: {
      activeTintColor: '#2563EB',
      inactiveTintColor: '#6B7280',
      style: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
      },
    },
  };
};
