@echo off
echo ========================================
echo    Tests JMeter pour weServeApp
echo ========================================
echo.

REM Vérifier si JMeter est installé dans le PATH
where jmeter >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ JMeter détecté dans le PATH!
    set JMETER_CMD=jmeter
    goto jmeter_found
)

REM Essayer de trouver JMeter dans les emplacements courants
echo 🔍 Recherche de JMeter dans les emplacements courants...
echo.

REM Emplacement 1: Program Files
if exist "C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files
    goto jmeter_found
)

REM Emplacement 2: Program Files (x86)
if exist "C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files (x86)
    goto jmeter_found
)

REM Emplacement 3: Dossier utilisateur
if exist "%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Downloads
    goto jmeter_found
)

REM Emplacement 4: Bureau
if exist "%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé sur le Bureau
    goto jmeter_found
)

REM Emplacement 5: Dossier racine C:
if exist "C:\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans C:\
    goto jmeter_found
)

REM Si JMeter n'est trouvé nulle part
echo ❌ JMeter n'a pas été trouvé automatiquement
echo.
echo Veuillez:
echo 1. Installer JMeter depuis: https://jmeter.apache.org/download_jmeter.cgi
echo 2. Ou ajouter JMeter au PATH système
echo 3. Ou spécifier manuellement le chemin
echo.
set /p custom_path="Chemin complet vers jmeter.bat (ou appuyez sur Entrée pour quitter): "
if not "%custom_path%"=="" (
    if exist "%custom_path%" (
        set JMETER_CMD="%custom_path%"
        echo ✅ Chemin personnalisé accepté
        goto jmeter_found
    ) else (
        echo ❌ Fichier non trouvé: %custom_path%
        pause
        exit /b 1
    )
) else (
    pause
    exit /b 1
)

:jmeter_found
echo.
echo 🎯 JMeter sera exécuté avec: %JMETER_CMD%
echo.

REM Créer le dossier de résultats s'il n'existe pas
if not exist "jmeter_results" mkdir jmeter_results

echo Choisissez le type de test à exécuter:
echo.
echo 1. Test de fumée (1 utilisateur, test rapide)
echo 2. Test de charge (10 utilisateurs, test complet)
echo 3. Test de stress (50 utilisateurs, test intensif)
echo 4. Test personnalisé
echo 5. Quitter
echo.

set /p choice="Votre choix (1-5): "

if "%choice%"=="1" goto smoke_test
if "%choice%"=="2" goto load_test
if "%choice%"=="3" goto stress_test
if "%choice%"=="4" goto custom_test
if "%choice%"=="5" goto exit
goto invalid_choice

:smoke_test
echo.
echo ========================================
echo    Exécution du test de fumée...
echo ========================================
echo.
echo Configuration:
echo - Utilisateurs: 1
echo - Durée: ~30 secondes
echo - Objectif: Validation de la configuration
echo.

set /p confirm="Confirmer l'exécution du test de fumée? (o/n): "
if /i "%confirm%"=="o" (
    echo Démarrage du test de fumée...
    %JMETER_CMD% -n -t weServeApp_TestPlan.jmx -l jmeter_results/smoke_test_results.jtl -e -o jmeter_results/smoke_test_report
    echo.
    echo Test de fumée terminé!
    echo Résultats disponibles dans: jmeter_results/smoke_test_report/
    echo Logs disponibles dans: jmeter_results/smoke_test_results.jtl
    echo.
    pause
    goto menu
) else (
    goto menu
)

:load_test
echo.
echo ========================================
echo    Exécution du test de charge...
echo ========================================
echo.
echo Configuration:
echo - Utilisateurs: 10
echo - Durée: ~2 minutes
echo - Objectif: Test de performance normale
echo.

set /p confirm="Confirmer l'exécution du test de charge? (o/n): "
if /i "%confirm%"=="o" (
    echo Démarrage du test de charge...
    %JMETER_CMD% -n -t weServeApp_TestPlan.jmx -l jmeter_results/load_test_results.jtl -e -o jmeter_results/load_test_report
    echo.
    echo Test de charge terminé!
    echo Résultats disponibles dans: jmeter_results/load_test_report/
    echo Logs disponibles dans: jmeter_results/load_test_results.jtl
    echo.
    pause
    goto menu
) else (
    goto menu
)

:stress_test
echo.
echo ========================================
echo    Exécution du test de stress...
echo ========================================
echo.
echo Configuration:
echo - Utilisateurs: 50
echo - Durée: ~5 minutes
echo - Objectif: Test des limites de performance
echo.

set /p confirm="Confirmer l'exécution du test de stress? (o/n): "
if /i "%confirm%"=="o" (
    echo Démarrage du test de stress...
    %JMETER_CMD% -n -t weServeApp_TestPlan.jmx -l jmeter_results/stress_test_results.jtl -e -o jmeter_results/stress_test_report
    echo.
    echo Test de stress terminé!
    echo Résultats disponibles dans: jmeter_results/stress_test_report/
    echo Logs disponibles dans: jmeter_results/stress_test_results.jtl
    echo.
    pause
    goto menu
) else (
    goto menu
)

:custom_test
echo.
echo ========================================
echo    Test personnalisé
echo ========================================
echo.
set /p users="Nombre d'utilisateurs: "
set /p duration="Durée en secondes: "
set /p rampup="Temps de montée en charge (secondes): "

echo.
echo Configuration personnalisée:
echo - Utilisateurs: %users%
echo - Durée: %duration% secondes
echo - Ramp-up: %rampup% secondes
echo.

set /p confirm="Confirmer l'exécution du test personnalisé? (o/n): "
if /i "%confirm%"=="o" (
    echo Démarrage du test personnalisé...
    %JMETER_CMD% -n -t weServeApp_TestPlan.jmx -l jmeter_results/custom_test_results.jtl -e -o jmeter_results/custom_test_report
    echo.
    echo Test personnalisé terminé!
    echo Résultats disponibles dans: jmeter_results/custom_test_report/
    echo Logs disponibles dans: jmeter_results/custom_test_results.jtl
    echo.
    pause
    goto menu
) else (
    goto menu
)

:invalid_choice
echo.
echo Choix invalide! Veuillez choisir entre 1 et 5.
echo.
pause
goto menu

:menu
echo.
echo ========================================
echo    Menu principal
echo ========================================
echo.
echo 1. Test de fumée (1 utilisateur, test rapide)
echo 2. Test de charge (10 utilisateurs, test complet)
echo 3. Test de stress (50 utilisateurs, test intensif)
echo 4. Test personnalisé
echo 5. Quitter
echo.
set /p choice="Votre choix (1-5): "

if "%choice%"=="1" goto smoke_test
if "%choice%"=="2" goto load_test
if "%choice%"=="3" goto stress_test
if "%choice%"=="4" goto custom_test
if "%choice%"=="5" goto exit
goto invalid_choice

:exit
echo.
echo Merci d'avoir utilisé les tests JMeter pour weServeApp!
echo.
pause
exit /b 0 