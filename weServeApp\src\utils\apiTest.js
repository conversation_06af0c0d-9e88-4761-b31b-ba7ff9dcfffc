// Fichier de test pour vérifier la connexion à l'API backend
import apiService from '../services/apiService';

// Test de connexion à l'API
export const testApiConnection = async () => {
  try {
    console.log('🧪 Test de connexion à l\'API...');
    
    // Test simple de connexion (sans authentification)
    const response = await fetch('http://*************:4000/api/v1/health');
    
    if (response.ok) {
      console.log('✅ API accessible !');
      return true;
    } else {
      console.log('❌ API accessible mais erreur HTTP:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Impossible de se connecter à l\'API:', error.message);
    return false;
  }
};

// Test de l'endpoint d'inscription
export const testSignupEndpoint = async () => {
  try {
    console.log('🧪 Test de l\'endpoint d\'inscription...');
    
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'test123456',
      phone: '**********',
      role: 'client'
    };
    
    const response = await fetch('http://*************:4000/api/v1/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Endpoint d\'inscription fonctionne !', result);
      return true;
    } else {
      console.log('❌ Erreur endpoint d\'inscription:', response.status, result);
      return false;
    }
  } catch (error) {
    console.log('❌ Erreur lors du test d\'inscription:', error.message);
    return false;
  }
};

// Test de l'endpoint des demandes de service
export const testServicesEndpoint = async () => {
  try {
    console.log('🧪 Test de l\'endpoint des services...');
    
    // Test de l'endpoint principal des services
    const response = await fetch('http://*************:4000/api/v1/services');
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Endpoint /services fonctionne !', data);
      return true;
    } else {
      console.log('❌ Erreur endpoint /services:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Erreur lors du test des services:', error.message);
    return false;
  }
};

// Test complet de l'API
export const runApiTests = async () => {
  console.log('🚀 Démarrage des tests API...');
  
  const connectionTest = await testApiConnection();
  const signupTest = await testSignupEndpoint();
  const servicesTest = await testServicesEndpoint();
  
  console.log('📊 Résultats des tests:');
  console.log('- Connexion API:', connectionTest ? '✅' : '❌');
  console.log('- Endpoint signup:', signupTest ? '✅' : '❌');
  console.log('- Endpoint services:', servicesTest ? '✅' : '❌');
  
  return {
    connection: connectionTest,
    signup: signupTest,
    services: servicesTest
  };
};

export default {
  testApiConnection,
  testSignupEndpoint,
  runApiTests
};
