import express from "express";
import { authenticate } from '../middlewares/auth.js';
//  IMPORTER le client global au lieu d'en créer un nouveau
import { twilioClient } from '../../server.js';

const router = express.Router();

// ❌ SUPPRIMER cette ligne qui crée un nouveau client
// const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

// 🔍 DEBUG - Vérification des variables Twilio
console.log('🔍 DEBUG - TWILIO_ACCOUNT_SID:', process.env.TWILIO_ACCOUNT_SID ? '✅ DÉFINI' : '❌ MANQUANT');
console.log('🔍 DEBUG - TWILIO_AUTH_TOKEN:', process.env.TWILIO_AUTH_TOKEN ? '✅ DÉFINI' : '❌ MANQUANT');
console.log('🔍 DEBUG - TWILIO_CONVERSATIONS_SERVICE_SID:', process.env.TWILIO_CONVERSATIONS_SERVICE_SID ? '✅ DÉFINI' : '❌ MANQUANT');

// Créer une conversation entre un client et un prestataire
router.post("/create-conversation", authenticate, async (req, res) => {
  try {
    // 🔍 DEBUG - Test de connexion Twilio
    console.log('🔍 DEBUG - Test de connexion Twilio...');
    try {
      //  UTILISER le client global
      const account = await twilioClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      console.log('✅ DEBUG - Twilio connecté, compte:', account.friendlyName);
    } catch (twilioErr) {
      console.error('❌ DEBUG - Erreur connexion Twilio:', twilioErr.message);
      return res.status(500).json({ 
        success: false, 
        error: 'Erreur de connexion Twilio: ' + twilioErr.message 
      });
    }

    const { serviceRequestId, providerId, providerName } = req.body;
    
    if (!serviceRequestId || !providerId) {
      return res.status(400).json({ 
        success: false, 
        error: "serviceRequestId et providerId sont requis" 
      });
    }

    console.log("🚀 Création conversation pour service:", serviceRequestId, "avec prestataire:", providerId);

    const serviceSid = process.env.TWILIO_CONVERSATIONS_SERVICE_SID;
    if (!serviceSid) {
      return res.status(500).json({ 
        success: false, 
        error: "TWILIO_CONVERSATIONS_SERVICE_SID non configuré" 
      });
    }

    // Créer la conversation avec un nom descriptif
    const conversationName = `Service-${serviceRequestId}-${providerName || providerId}`;
    const uniqueName = `service_${serviceRequestId}_${providerId}_${Date.now()}`;
    
    //  UTILISER le client global
    const conversation = await twilioClient.conversations.v1.services(serviceSid).conversations.create({
      friendlyName: conversationName,
      uniqueName: uniqueName,
      attributes: JSON.stringify({
        serviceRequestId,
        providerId,
        clientId: req.user.id,
        type: 'service_request'
      })
    });

    console.log("✅ Conversation créée:", conversation.sid);

    // 🔍 DEBUG - Test du service après création de conversation
    console.log("🔍 DEBUG - Test du service après création...");
    try {
      const service = await twilioClient.conversations.v1.services(serviceSid).fetch();
      console.log("✅ Service accessible:", service.friendlyName);
      
      // Test d'accès à la conversation créée
      const createdConversation = await twilioClient.conversations.v1
        .services(serviceSid)
        .conversations(conversation.sid)
        .fetch();
      console.log("✅ Conversation accessible:", createdConversation.friendlyName);
      
    } catch (serviceErr) {
      console.error("❌ Erreur accès service/conversation:", serviceErr.message);
    }

    res.json({ 
      success: true,
      conversationSid: conversation.sid,
      friendlyName: conversation.friendlyName
    });

  } catch (err) {
    console.error("❌ Erreur création conversation:", err);
    res.status(500).json({ 
      success: false, 
      error: err.message 
    });
  }
});

// Ajouter un participant à une conversation
router.post("/add-participant", authenticate, async (req, res) => {
  try {
    const { identity, conversationSid, participantType } = req.body;
    
    if (!identity || !conversationSid || !participantType) {
      return res.status(400).json({ 
        success: false, 
        error: "identity, conversationSid et participantType sont requis" 
      });
    }

    console.log("👤 Ajout participant:", identity, "dans conversation:", conversationSid);

    // 🔧 CORRECTION : Utiliser le service de conversations pour ajouter le participant
    const serviceSid = process.env.TWILIO_CONVERSATIONS_SERVICE_SID;
    if (!serviceSid) {
      return res.status(500).json({ 
        success: false, 
        error: "TWILIO_CONVERSATIONS_SERVICE_SID non configuré" 
      });
    }

    // ✅ CORRECT : Ajouter le participant via le service
    const participant = await twilioClient.conversations.v1
      .services(serviceSid)
      .conversations(conversationSid)
      .participants.create({
        identity: identity,
        attributes: JSON.stringify({
          participantType, // 'client' ou 'provider'
          userId: req.user.id
        })
      });

    console.log("✅ Participant ajouté:", participant.sid);

    res.json({ 
      success: true,
      participant: {
        sid: participant.sid,
        identity: participant.identity,
        participantType
      }
    });

  } catch (err) {
    console.error("❌ Erreur ajout participant:", err);
    
    // 🔍 DEBUG - Plus de détails sur l'erreur
    if (err.code === 50050) {
      console.error("💡 Erreur 50050: Service instance not found");
      console.error("Vérifiez que TWILIO_CONVERSATIONS_SERVICE_SID est correct");
      console.error("Service SID utilisé:", process.env.TWILIO_CONVERSATIONS_SERVICE_SID);
    }
    
    res.status(500).json({ 
      success: false, 
      error: err.message,
      code: err.code,
      details: err.moreInfo
    });
  }
});

// Envoyer un message dans une conversation
router.post("/send-message", authenticate, async (req, res) => {
  try {
    const { conversationSid, body, messageType = 'text' } = req.body;
    
    if (!conversationSid || !body) {
      return res.status(400).json({ 
        success: false, 
        error: "conversationSid et body sont requis" 
      });
    }

    console.log("💬 Envoi message dans conversation:", conversationSid);

    // 🔧 CORRECTION : Utiliser le service de conversations pour envoyer le message
    const serviceSid = process.env.TWILIO_CONVERSATIONS_SERVICE_SID;
    if (!serviceSid) {
      return res.status(500).json({ 
        success: false, 
        error: "TWILIO_CONVERSATIONS_SERVICE_SID non configuré" 
      });
    }

    // ✅ CORRECT : Envoyer le message via le service
    const message = await twilioClient.conversations.v1
      .services(serviceSid)
      .conversations(conversationSid)
      .messages.create({
        author: req.user.id,
        body: body,
        attributes: JSON.stringify({
          messageType,
          senderId: req.user.id,
          senderRole: req.user.role
        })
      });

    console.log("✅ Message envoyé:", message.sid);

    res.json({ 
      success: true,
      message: {
        sid: message.sid,
        body: message.body,
        author: message.author,
        dateCreated: message.dateCreated
      }
    });

  } catch (err) {
    console.error("❌ Erreur envoi message:", err);
    
    // 🔍 DEBUG - Plus de détails sur l'erreur
    if (err.code === 50050) {
      console.error("💡 Erreur 50050: Service instance not found");
      console.error("Vérifiez que TWILIO_CONVERSATIONS_SERVICE_SID est correct");
      console.error("Service SID utilisé:", process.env.TWILIO_CONVERSATIONS_SERVICE_SID);
    }
    
    res.status(500).json({ 
      success: false, 
      error: err.message,
      code: err.code,
      details: err.moreInfo
    });
  }
});

// Récupérer les messages d'une conversation
router.get("/messages/:conversationSid", authenticate, async (req, res) => {
  try {
    const { conversationSid } = req.params;
    const { limit = 50 } = req.query;

    console.log("📥 Récupération messages conversation:", conversationSid);

    // 🔧 CORRECTION : Utiliser le service de conversations pour récupérer les messages
    const serviceSid = process.env.TWILIO_CONVERSATIONS_SERVICE_SID;
    if (!serviceSid) {
      return res.status(500).json({ 
        success: false, 
        error: "TWILIO_CONVERSATIONS_SERVICE_SID non configuré" 
      });
    }

    // ✅ CORRECT : Récupérer les messages via le service
    const messages = await twilioClient.conversations.v1
      .services(serviceSid)
      .conversations(conversationSid)
      .messages.list({ limit: parseInt(limit) });

    console.log("✅ Messages récupérés:", messages.length);

    res.json({ 
      success: true,
      messages: messages.map(msg => ({
        sid: msg.sid,
        body: msg.body,
        author: msg.author,
        dateCreated: msg.dateCreated,
        attributes: msg.attributes
      }))
    });

  } catch (err) {
    console.error("❌ Erreur récupération messages:", err);
    
    // 🔍 DEBUG - Plus de détails sur l'erreur
    if (err.code === 50050) {
      console.error("💡 Erreur 50050: Service instance not found");
      console.error("Vérifiez que TWILIO_CONVERSATIONS_SERVICE_SID est correct");
      console.error("Service SID utilisé:", process.env.TWILIO_CONVERSATIONS_SERVICE_SID);
    }
    
    res.status(500).json({ 
      success: false, 
      error: err.message,
      code: err.code,
      details: err.moreInfo
    });
  }
});

// Lister les conversations d'un utilisateur
router.get("/conversations", authenticate, async (req, res) => {
  try {
    console.log("🔍 Récupération conversations utilisateur:", req.user.id);

    const serviceSid = process.env.TWILIO_CONVERSATIONS_SERVICE_SID;
    if (!serviceSid) {
      return res.status(500).json({ 
        success: false, 
        error: "TWILIO_CONVERSATIONS_SERVICE_SID non configuré" 
      });
    }

    // ✅ CORRECT : Lister les conversations via le service
    const conversations = await twilioClient.conversations.v1.services(serviceSid).conversations.list();

    // Filtrer les conversations où l'utilisateur est participant
    const userConversations = conversations.filter(conv => {
      try {
        const attributes = JSON.parse(conv.attributes || '{}');
        return attributes.clientId === req.user.id || attributes.providerId === req.user.id;
      } catch {
        return false;
      }
    });

    console.log("✅ Conversations trouvées:", userConversations.length);

    res.json({ 
      success: true,
      conversations: userConversations.map(conv => ({
        sid: conv.sid,
        friendlyName: conv.friendlyName,
        dateCreated: conv.dateCreated,
        attributes: conv.attributes
      }))
    });

  } catch (err) {
    console.error("❌ Erreur récupération conversations:", err);
    
    // 🔍 DEBUG - Plus de détails sur l'erreur
    if (err.code === 50050) {
      console.error("💡 Erreur 50050: Service instance not found");
      console.error("Vérifiez que TWILIO_CONVERSATIONS_SERVICE_SID est correct");
      console.error("Service SID utilisé:", process.env.TWILIO_CONVERSATIONS_SERVICE_SID);
    }
    
    res.status(500).json({ 
      success: false, 
      error: err.message,
      code: err.code,
      details: err.moreInfo
    });
  }
});

export default router;
