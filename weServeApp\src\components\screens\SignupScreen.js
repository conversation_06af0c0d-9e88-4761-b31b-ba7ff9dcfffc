import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import Button from '../common/Button';
import Input from '../common/Input';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuthStore } from '../../stores/authStore';
import authService from '../../services/authService';

const SignupScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'client'
  });
  
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  
  const { setError, clearError } = useAuthStore();

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Effacer l'erreur du champ modifié
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Le téléphone est requis';
    } else if (!/^[\+]?[0-9\s\-\(\)]{10,}$/.test(formData.phone)) {
      newErrors.phone = 'Format de téléphone invalide';
    }
    
    if (!formData.password.trim()) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }
    
    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignup = async () => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    clearError();
    
    try {
      // Préparation des données pour l'API
      const signupData = {
        name: `${formData.firstName.trim()} ${formData.lastName.trim()}`,
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        password: formData.password,
        role: formData.role
      };
      
      // Appel à l'API d'inscription
      const result = await authService.signup(signupData);
      
      console.log('🔍 Résultat signup:', result);
      
      if (result.success) {
        Alert.alert(
          'Inscription réussie !',
          'Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Login')
            }
          ]
        );
      } else {
        // Gestion des erreurs de l'API
        const errorMessage = result.message || 'Erreur lors de l\'inscription';
        console.log('❌ Erreur signup:', errorMessage);
        setError(errorMessage);
        Alert.alert('Erreur', errorMessage);
      }
    } catch (error) {
      // Gestion des erreurs de connexion
      setError('Erreur de connexion lors de l\'inscription. Veuillez réessayer.');
      Alert.alert('Erreur', 'Erreur de connexion. Veuillez vérifier votre connexion internet et réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleRole = () => {
    const newRole = formData.role === 'client' ? 'provider' : 'client';
    updateFormData('role', newRole);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color={COLORS.dark} />
            </TouchableOpacity>
            <Text style={styles.title}>Créer un compte</Text>
            <Text style={styles.subtitle}>Rejoignez la communauté WeServe</Text>
          </View>

          {/* Sélection du rôle */}
          <View style={styles.roleContainer}>
            <Text style={styles.roleLabel}>Je suis un :</Text>
            <View style={styles.roleToggle}>
              <TouchableOpacity
                style={[
                  styles.roleButton,
                  formData.role === 'client' && styles.roleButtonActive
                ]}
                onPress={() => updateFormData('role', 'client')}
              >
                <Ionicons 
                  name="person" 
                  size={20} 
                  color={formData.role === 'client' ? COLORS.white : COLORS.gray} 
                />
                <Text style={[
                  styles.roleText,
                  formData.role === 'client' && styles.roleTextActive
                ]}>
                  Client
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.roleButton,
                  formData.role === 'provider' && styles.roleButtonActive
                ]}
                onPress={() => updateFormData('role', 'provider')}
              >
                <Ionicons 
                  name="construct" 
                  size={20} 
                  color={formData.role === 'provider' ? COLORS.white : COLORS.gray} 
                />
                <Text style={[
                  styles.roleText,
                  formData.role === 'provider' && styles.roleTextActive
                ]}>
                  Prestataire
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Formulaire d'inscription */}
          <View style={styles.formContainer}>
            <View style={styles.nameRow}>
              <Input
                label="Prénom"
                placeholder="Votre prénom"
                value={formData.firstName}
                onChangeText={(value) => updateFormData('firstName', value)}
                leftIcon="person"
                error={errors.firstName}
                style={styles.halfInput}
              />
              
              <Input
                label="Nom"
                placeholder="Votre nom"
                value={formData.lastName}
                onChangeText={(value) => updateFormData('lastName', value)}
                leftIcon="person"
                error={errors.lastName}
                style={styles.halfInput}
              />
            </View>

            <Input
              label="Email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail"
              error={errors.email}
            />

            <Input
              label="Téléphone"
              placeholder="+33 1 23 45 67 89"
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              keyboardType="phone-pad"
              leftIcon="call"
              error={errors.phone}
            />

            <Input
              label="Mot de passe"
              placeholder="Votre mot de passe"
              value={formData.password}
              onChangeText={(value) => updateFormData('password', value)}
              secureTextEntry
              leftIcon="lock-closed"
              error={errors.password}
            />

            <Input
              label="Confirmer le mot de passe"
              placeholder="Confirmez votre mot de passe"
              value={formData.confirmPassword}
              onChangeText={(value) => updateFormData('confirmPassword', value)}
              secureTextEntry
              leftIcon="lock-closed"
              error={errors.confirmPassword}
            />

            <Button
              title="Créer mon compte"
              onPress={handleSignup}
              loading={isLoading}
              style={styles.signupButton}
            />

            {/* Lien vers connexion */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>Déjà un compte ? </Text>
              <Button
                title="Se connecter"
                variant="outline"
                size="small"
                onPress={() => navigation.navigate('Login')}
                style={styles.loginButton}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: SIZES.padding,
  },
  header: {
    marginTop: SIZES.large,
    marginBottom: SIZES.extraLarge,
  },
  backButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
    padding: SIZES.base,
  },
  title: {
    fontSize: 28,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    textAlign: 'center',
    marginBottom: SIZES.base,
  },
  subtitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
    textAlign: 'center',
  },
  roleContainer: {
    marginBottom: SIZES.extraLarge,
  },
  roleLabel: {
    fontSize: SIZES.medium,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: SIZES.medium,
    textAlign: 'center',
  },
  roleToggle: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.base,
    ...SHADOWS.small,
  },
  roleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.medium,
    paddingHorizontal: SIZES.padding,
    borderRadius: SIZES.radius,
    marginHorizontal: SIZES.base,
  },
  roleButtonActive: {
    backgroundColor: COLORS.primary,
  },
  roleText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.gray,
    marginLeft: SIZES.base,
  },
  roleTextActive: {
    color: COLORS.white,
  },
  formContainer: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius * 2,
    padding: SIZES.padding * 1.5,
    ...SHADOWS.medium,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    flex: 0.48,
  },
  signupButton: {
    marginTop: SIZES.large,
    marginBottom: SIZES.extraLarge,
  },
  loginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loginText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
  loginButton: {
    marginLeft: SIZES.base,
  },
});

export default SignupScreen;
