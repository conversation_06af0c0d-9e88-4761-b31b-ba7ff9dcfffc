import passport from 'passport';
import { signup as signupSvc, login as loginSvc, socialLogin, refresh as refreshSvc, requestPasswordReset, resetPassword } from '../services/authService.js';
import { refreshSchema, requestResetSchema, resetPasswordSchema } from '../utils/joiSchemas.js';

const isProd = process.env.NODE_ENV === 'production';
const cookieOpts = {
  httpOnly: true,
  sameSite: isProd ? 'none' : 'lax',
  secure: isProd,
  // Access token short-lived; refresh longer
};

function setAuthCookies(res, { accessToken, refreshToken }) {
  if (accessToken) res.cookie('accessToken', accessToken, { ...cookieOpts, maxAge: 15 * 60 * 1000 });
  if (refreshToken) res.cookie('refreshToken', refreshToken, { ...cookieOpts, maxAge: 7 * 24 * 60 * 60 * 1000 });
}

function clearAuthCookies(res) {
  res.clearCookie('accessToken', { ...cookieOpts, maxAge: undefined });
  res.clearCookie('refreshToken', { ...cookieOpts, maxAge: undefined });
}

export async function signup(req, res, next) {
  try {
    const { name, email, phone, password, role } = req.body;
    const result = await signupSvc({ name, email, phone, password, role });
    setAuthCookies(res, result);
    res.status(201).json({ user: sanitizeUser(result.user) });
  } catch (err) { next(err); }
}

export async function login(req, res, next) {
  try {
    const { email, phone, password } = req.body;
    const result = await loginSvc({ email, phone, password });
    setAuthCookies(res, result);
    res.json({ user: sanitizeUser(result.user) });
  } catch (err) { next(err); }
}

export const googleAuth = passport.authenticate('google', { scope: ['profile', 'email'], session: false });
export const googleCallback = (req, res, next) => {
  passport.authenticate('google', { session: false }, async (err, user) => {
    if (err) return next(err);
    try {
      const result = await socialLogin(user);
      setAuthCookies(res, result);
      res.json({ user: sanitizeUser(result.user) });
    } catch (e) { next(e); }
  })(req, res, next);
};

export const facebookAuth = passport.authenticate('facebook', { scope: ['email'], session: false });
export const facebookCallback = (req, res, next) => {
  passport.authenticate('facebook', { session: false }, async (err, user) => {
    if (err) return next(err);
    try {
      const result = await socialLogin(user);
      setAuthCookies(res, result);
      res.json({ user: sanitizeUser(result.user) });
    } catch (e) { next(e); }
  })(req, res, next);
};

export async function refresh(req, res, next) {
  try {
    const { error, value } = refreshSchema.validate(req.body.refreshToken ? req.body : { refreshToken: req.cookies?.refreshToken });
    if (error) return res.status(400).json({ message: 'Validation error' });
    const result = await refreshSvc(value.refreshToken);
    setAuthCookies(res, result);
    res.json({ ok: true });
  } catch (err) { next(err); }
}

export async function logout(_req, res) {
  clearAuthCookies(res);
  res.json({ message: 'Logged out' });
}

export async function requestReset(req, res, next) {
  try {
    const { error, value } = requestResetSchema.validate(req.body);
    if (error) return res.status(400).json({ message: 'Validation error' });
    await requestPasswordReset(value);
    res.json({ message: 'If account exists, reset instructions sent' });
  } catch (err) { next(err); }
}

export async function doReset(req, res, next) {
  try {
    const { error, value } = resetPasswordSchema.validate(req.body);
    if (error) return res.status(400).json({ message: 'Validation error' });
    await resetPassword(value);
    res.json({ message: 'Password updated' });
  } catch (err) { next(err); }
}

function sanitizeUser(user) {
  return {
    id: user._id,
    name: user.name,
    email: user.email,
    phone: user.phone,
    avatarUrl: user.avatarUrl,
    role: user.role,
    averageRating: user.averageRating,
    totalRatings: user.totalRatings
  };
} 