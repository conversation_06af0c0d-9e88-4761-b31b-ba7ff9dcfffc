import mongoose from 'mongoose';

export async function connectToDatabase() {
  try {
    const uri = process.env.MONGODB_URI;
    
    if (!uri) {
      throw new Error('MONGODB_URI environment variable is not defined');
    }

    mongoose.set('strictQuery', true);
    
    await mongoose.connect(uri, {
      autoIndex: true,
      serverSelectionTimeoutMS: 5000, // Timeout après 5 secondes
      socketTimeoutMS: 45000, // Timeout des sockets
      maxPoolSize: 10, // Taille maximale du pool de connexions
      minPoolSize: 1, // Taille minimale du pool de connexions
    });
    
    console.log('✅ Connected to MongoDB successfully');
    
    // Gestion des événements de connexion
    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.log('⚠️ MongoDB disconnected');
    });
    
    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconnected');
    });
    
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    
    if (error.message.includes('IP whitelist')) {
      console.log('\n💡 Solution: Ajoutez votre adresse IP actuelle à la liste blanche MongoDB Atlas');
      console.log('   - Allez sur https://cloud.mongodb.com');
      console.log('   - Sélectionnez votre cluster');
      console.log('   - Cliquez sur "Network Access"');
      console.log('   - Ajoutez votre IP actuelle ou 0.0.0.0/0 pour toutes les IPs');
    }
    
    throw error;
  }
} 


