import { Platform } from 'react-native';

// Configuration pour résoudre les problèmes de gesture handler sur Expo SDK 52
export const gestureHandlerConfig = {
  // Configuration de base
  base: {
    // Désactiver les fonctionnalités problématiques
    enableScreens: false,
    enableAnimations: false,
    enableGestures: false,
  },
  
  // Configuration spécifique pour gesture handler
  gestureHandler: {
    // Configuration des gestes
    gestureEnabled: false,
    
    // Configuration des transitions
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
    
    // Configuration des animations
    animationEnabled: false,
    
    // Configuration des safe areas
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 },
  },
  
  // Configuration par plateforme
  platform: {
    android: {
      // Désactiver complètement sur Android
      enableScreens: false,
      enableAnimations: false,
      enableGestures: false,
      enableSafeArea: false,
    },
    ios: {
      // Configuration minimale sur iOS
      enableScreens: false,
      enableAnimations: false,
      enableGestures: false,
      enableSafeArea: false,
    },
  },
};

// Fonction pour obtenir la configuration selon la plateforme
export const getGestureHandlerConfig = () => {
  const platform = Platform.OS;
  return {
    ...gestureHandlerConfig.base,
    ...gestureHandlerConfig.gestureHandler,
    ...gestureHandlerConfig.platform[platform],
  };
};

// Configuration pour éviter les erreurs de gesture handler
export const getSafeGestureConfig = () => {
  return {
    // Désactiver toutes les fonctionnalités problématiques
    enableScreens: false,
    enableAnimations: false,
    enableGestures: false,
    enableSafeArea: false,
    
    // Configuration des écrans
    screenOptions: {
      headerShown: false,
      gestureEnabled: false,
      animationEnabled: false,
      // Transition simple
      cardStyleInterpolator: ({ current, layouts }) => ({
        cardStyle: {
          transform: [
            {
              translateX: current.progress.interpolate({
                inputRange: [0, 1],
                outputRange: [layouts.screen.width, 0],
              }),
            },
          ],
        },
      }),
    },
    
    // Configuration des onglets
    tabBarOptions: {
      activeTintColor: '#2563EB',
      inactiveTintColor: '#6B7280',
      style: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
      },
    },
  };
};

export default gestureHandlerConfig;
