import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import Button from '../common/Button';
import Card from '../common/Card';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuthStore } from '../../stores/authStore';

const ProfileScreen = ({ navigation }) => {
  const { user, userRole, logout } = useAuthStore();
  const [isEditing, setIsEditing] = useState(false);

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Déconnexion',
          style: 'destructive',
          onPress: () => {
            logout();
            // Navigation automatique vers l'écran de connexion
            // navigation.replace('Login');
          },
        },
      ]
    );
  };

  const handleEditProfile = () => {
    setIsEditing(!isEditing);
    // Ici vous pourriez naviguer vers un écran d'édition
    Alert.alert('Modifier le profil', 'Fonctionnalité à implémenter');
  };

  const renderProfileHeader = () => (
    <View style={styles.profileHeader}>
      <View style={styles.avatarContainer}>
        {user?.avatar ? (
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Ionicons 
              name={userRole === 'client' ? 'person' : 'construct'} 
              size={40} 
              color={COLORS.white} 
            />
          </View>
        )}
      </View>
      
      <Text style={styles.userName}>
        {user?.firstName} {user?.lastName}
      </Text>
      
      <Text style={styles.userRole}>
        {userRole === 'client' ? 'Client' : 'Prestataire de services'}
      </Text>
      
      <Text style={styles.userEmail}>{user?.email}</Text>
      
      <Button
        title="Modifier le profil"
        variant="outline"
        size="small"
        onPress={handleEditProfile}
        style={styles.editButton}
      />
    </View>
  );

  const renderProfileSection = (title, items) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <Card style={styles.sectionCard}>
        {items.map((item, index) => (
          <View key={index} style={styles.sectionItem}>
            <View style={styles.itemIcon}>
              <Ionicons name={item.icon} size={20} color={item.color} />
            </View>
            <View style={styles.itemContent}>
              <Text style={styles.itemTitle}>{item.title}</Text>
              <Text style={styles.itemValue}>{item.value}</Text>
            </View>
            {item.action && (
              <TouchableOpacity style={styles.itemAction} onPress={item.action}>
                <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
              </TouchableOpacity>
            )}
          </View>
        ))}
      </Card>
    </View>
  );

  const renderSettingsSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Paramètres</Text>
      <Card style={styles.sectionCard}>
        <TouchableOpacity style={styles.settingItem} onPress={() => Alert.alert('Notifications', 'Paramètres des notifications à implémenter')}>
          <View style={styles.settingIcon}>
            <Ionicons name="notifications" size={20} color={COLORS.primary} />
          </View>
          <Text style={styles.settingTitle}>Notifications</Text>
          <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.settingItem} onPress={() => Alert.alert('Confidentialité', 'Paramètres de confidentialité à implémenter')}>
          <View style={styles.settingIcon}>
            <Ionicons name="shield-checkmark" size={20} color={COLORS.primary} />
          </View>
          <Text style={styles.settingTitle}>Confidentialité</Text>
          <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.settingItem} onPress={() => Alert.alert('Aide', 'Centre d\'aide à implémenter')}>
          <View style={styles.settingIcon}>
            <Ionicons name="help-circle" size={20} color={COLORS.primary} />
          </View>
          <Text style={styles.settingTitle}>Aide et support</Text>
          <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
        </TouchableOpacity>
      </Card>
    </View>
  );

  const renderLogoutSection = () => (
    <View style={styles.section}>
      <Card style={styles.sectionCard}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out" size={20} color={COLORS.danger} />
          <Text style={styles.logoutText}>Se déconnecter</Text>
        </TouchableOpacity>
      </Card>
    </View>
  );

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement du profil...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const profileItems = [
    {
      icon: 'call',
      title: 'Téléphone',
      value: user.phone || 'Non renseigné',
      color: COLORS.primary,
    },
    {
      icon: 'location',
      title: 'Localisation',
      value: 'Paris, France', // À remplacer par la vraie localisation
      color: COLORS.secondary,
    },
  ];

  if (userRole === 'provider') {
    profileItems.push(
      {
        icon: 'star',
        title: 'Note moyenne',
        value: '4.8/5.0', // À remplacer par la vraie note
        color: COLORS.accent,
      },
      {
        icon: 'checkmark-circle',
        title: 'Services complétés',
        value: '127', // À remplacer par le vrai nombre
        color: COLORS.success,
      }
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {renderProfileHeader()}
        {renderProfileSection('Informations personnelles', profileItems)}
        {renderSettingsSection()}
        {renderLogoutSection()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  profileHeader: {
    backgroundColor: COLORS.primary,
    paddingVertical: 40,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    marginBottom: 20,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: COLORS.white,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: COLORS.white,
  },
  userName: {
    fontSize: 24,
    fontFamily: FONTS.bold,
    color: COLORS.white,
    marginBottom: 8,
    textAlign: 'center',
  },
  userRole: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.white,
    marginBottom: 8,
    textAlign: 'center',
    opacity: 0.9,
  },
  userEmail: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.white,
    marginBottom: 20,
    textAlign: 'center',
    opacity: 0.8,
  },
  editButton: {
    backgroundColor: 'transparent',
    borderColor: COLORS.white,
  },
  section: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: 12,
  },
  sectionCard: {
    padding: 0,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  itemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: 4,
  },
  itemValue: {
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
  itemAction: {
    padding: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingTitle: {
    flex: 1,
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  logoutText: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    color: COLORS.danger,
    marginLeft: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
});

export default ProfileScreen;
