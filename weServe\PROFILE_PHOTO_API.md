# API Gestion des Photos de Profil - WeServe

Ce document décrit les nouvelles routes API pour gérer les photos de profil des utilisateurs dans WeServe.

## Routes Disponibles

### 1. Récupérer le Profil Utilisateur

**GET** `/api/v1/auth/profile`

Récupère le profil complet de l'utilisateur authentifié.

**Headers requis :**
```
Authorization: Bearer <JWT_TOKEN>
```

**Réponse de succès (200) :**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "name": "Nom de l'utilisateur",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "avatarUrl": "https://cloudinary.com/...",
    "role": "client",
    "averageRating": 4.5,
    "totalRatings": 10,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. Mettre à Jour la Photo de Profil

**POST** `/api/v1/auth/update-profile-photo`

Met à jour la photo de profil de l'utilisateur authentifié.

**Headers requis :**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: multipart/form-data
```

**Body requis :**
- `photo` : Fichier image (JPG, PNG, etc.) - Maximum 10MB

**Réponse de succès (200) :**
```json
{
  "success": true,
  "message": "Photo de profil mise à jour avec succès",
  "user": {
    "id": "user_id",
    "name": "Nom de l'utilisateur",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "avatarUrl": "https://cloudinary.com/...",
    "role": "client",
    "averageRating": 4.5,
    "totalRatings": 10
  }
}
```

**Erreurs possibles :**
- `400` : Aucune photo fournie
- `401` : Non authentifié
- `500` : Erreur serveur

### 3. Supprimer la Photo de Profil

**DELETE** `/api/v1/auth/profile-photo`

Supprime la photo de profil de l'utilisateur authentifié.

**Headers requis :**
```
Authorization: Bearer <JWT_TOKEN>
```

**Réponse de succès (200) :**
```json
{
  "success": true,
  "message": "Photo de profil supprimée avec succès",
  "user": {
    "id": "user_id",
    "name": "Nom de l'utilisateur",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "avatarUrl": null,
    "role": "client",
    "averageRating": 4.5,
    "totalRatings": 10
  }
}
```

## Utilisation avec JavaScript/Frontend

### Exemple de mise à jour de photo de profil

```javascript
const updateProfilePhoto = async (file) => {
  const formData = new FormData();
  formData.append('photo', file);

  try {
    const response = await fetch('/api/v1/auth/update-profile-photo', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Photo mise à jour:', result.user.avatarUrl);
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
};
```

### Exemple de récupération du profil

```javascript
const getProfile = async () => {
  try {
    const response = await fetch('/api/v1/auth/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Profil:', result.user);
      // Afficher la photo de profil
      if (result.user.avatarUrl) {
        document.getElementById('profile-photo').src = result.user.avatarUrl;
      }
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
};
```

### Exemple de suppression de photo

```javascript
const deleteProfilePhoto = async () => {
  try {
    const response = await fetch('/api/v1/auth/profile-photo', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Photo supprimée');
      // Masquer la photo de profil
      document.getElementById('profile-photo').style.display = 'none';
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
};
```

## Stockage des Images

Les photos de profil sont stockées sur **Cloudinary** dans le dossier `weserve/profile-photos` et les URLs sont sauvegardées dans le champ `avatarUrl` du modèle User.

## Sécurité

- Toutes les routes nécessitent une authentification JWT
- Seuls les fichiers image sont acceptés
- Taille maximale : 10MB par fichier
- Validation des types MIME

## Tests

Un fichier de test complet est disponible dans `tests/profilePhoto.test.js` pour vérifier le bon fonctionnement de toutes les routes.
