import dotenv from 'dotenv';
dotenv.config();

import http from 'http';
import app from './src/app.js';
import { connectToDatabase } from './src/config/db.js';
import twilio from 'twilio';

const PORT = process.env.PORT || 5000;

// Test de diagnostic des variables d'environnement
console.log('=== DIAGNOSTIC DES VARIABLES ===');
console.log('PORT:', PORT);
console.log('NODE_ENV:', process.env.NODE_ENV);

console.log('\n=== TWILIO ===');
console.log('TWILIO_ACCOUNT_SID:', process.env.TWILIO_ACCOUNT_SID ? '✅ DÉFINI' : '❌ MANQUANT');
console.log('TWILIO_AUTH_TOKEN:', process.env.TWILIO_AUTH_TOKEN ? '✅ DÉFINI' : '❌ MANQUANT');
console.log('TWILIO_CONVERSATIONS_SERVICE_SID:', process.env.TWILIO_CONVERSATIONS_SERVICE_SID ? '✅ DÉFINI' : '❌ MANQUANT');

console.log('================================');

const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

//  EXPORTER le client Twilio pour l'utiliser dans les routes
export { client as twilioClient };

async function start() {
  await connectToDatabase();

  try {
    console.log('🔍 Test de connexion Twilio...');
    console.log('Account SID:', process.env.TWILIO_ACCOUNT_SID ? '✅' : '❌');
    console.log('Auth Token:', process.env.TWILIO_AUTH_TOKEN ? '✅' : '❌');
    console.log('Conversations Service SID:', process.env.TWILIO_CONVERSATIONS_SERVICE_SID ? '✅' : '❌');
    
    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
      console.log('Service SID:', process.env.TWILIO_CONVERSATIONS_SERVICE_SID);
    }
    
    // Test 1: Vérifier que l'API Twilio fonctionne
    console.log('🧪 Test 1: Vérification de l\'API Twilio...');
    const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    console.log('✅ API Twilio accessible, compte:', account.friendlyName);
    
    // Test 2: Lister les services disponibles
    console.log('🧪 Test 2: Liste des services disponibles...');
    try {
      const services = await client.conversations.v1.services.list();
      console.log('✅ Services trouvés:', services.length);
      services.forEach(service => {
        console.log(`  - ${service.friendlyName} (${service.sid})`);
      });
    } catch (serviceErr) {
      console.error('❌ Erreur liste services:', serviceErr.message);
    }
    
    // Test 3: Accéder au service spécifique
    console.log('🧪 Test 3: Accès au service spécifique...');
    try {
      const service = await client.conversations.v1.services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID).fetch();
      console.log('✅ Service accessible:', service.friendlyName);
      
      // Test 4: Lister les conversations de ce service
      const conversations = await client.conversations.v1.services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID).conversations.list();
      console.log(`✅ Twilio connecté — ${conversations.length} conversations trouvées`);
    } catch (serviceErr) {
      console.error('❌ Erreur accès service:', serviceErr.message);
      console.error('Code d\'erreur:', serviceErr.code);
      
      if (serviceErr.code === 50050) {
        console.error('💡 SOLUTION: Le service n\'existe pas ou n\'est pas accessible');
        console.error('1. Vérifiez que le SID est correct');
        console.error('2. Vérifiez que le service est dans le bon environnement (sandbox/production)');
        console.error('3. Vérifiez que votre compte a accès aux Conversations');
      }
    }
    
  } catch (err) {
    console.error('❌ Erreur connexion Twilio :', err.message);
    console.error('Code d\'erreur:', err.code);
    console.error('Plus de détails:', err);
  }

  const server = http.createServer(app);
  server.listen(PORT, () => {
    console.log(`🚀 WeServe API listening on port ${PORT}`);
    console.log(`📸 Système d'upload local activé (base64)`);
  });
}

start().catch((err) => {
  console.error('Failed to start server:', err);
  process.exit(1);
});
