import ServiceRequest from '../models/ServiceRequest.js';

export async function createServiceRequest({ title, description, category, photos, location, date, budget, clientId }) {
  const doc = await ServiceRequest.create({
    title,
    description,
    category,
    photos: photos || [],
    location: {
      address: location.address,
      coordinates: {
        type: 'Point',
        coordinates: location.coordinates.coordinates // [longitude, latitude]
      }
    },
    date,
    budget,
    clientId
  });
  return doc;
}

export async function listOpenRequests() {
  return ServiceRequest.find({ status: 'pending' }).sort({ createdAt: -1 });
}

export async function getRequestById(id) {
  return ServiceRequest.findById(id);
}

export async function updateStatus({ id, status, user }) {
  const sr = await ServiceRequest.findById(id);
  if (!sr) throw Object.assign(new Error('Not found'), { status: 404 });
  
  if (status === 'accepted') {
    if (user.role !== 'provider') throw Object.assign(new Error('Only providers can accept'), { status: 403 });
    if (sr.status !== 'pending') throw Object.assign(new Error('Cannot accept non-pending'), { status: 400 });
    sr.status = 'accepted';
    sr.providerId = user.id;
  } else if (status === 'in_progress') {
    if (user.role !== 'provider') throw Object.assign(new Error('Only providers can start work'), { status: 403 });
    if (sr.status !== 'accepted' || sr.providerId?.toString() !== user.id) throw Object.assign(new Error('Only assigned provider can start work'), { status: 400 });
    sr.status = 'in_progress';
  } else if (status === 'completed') {
    if (user.role !== 'provider') throw Object.assign(new Error('Only providers can complete'), { status: 403 });
    if (sr.status !== 'in_progress' || sr.providerId?.toString() !== user.id) throw Object.assign(new Error('Only assigned provider can complete'), { status: 400 });
    sr.status = 'completed';
  } else if (status === 'cancelled') {
    if (user.role !== 'client' || sr.clientId.toString() !== user.id) throw Object.assign(new Error('Only owner client can cancel'), { status: 403 });
    if (sr.status === 'completed') throw Object.assign(new Error('Cannot cancel completed'), { status: 400 });
    sr.status = 'cancelled';
  } else if (status === 'pending') {
    throw Object.assign(new Error('Invalid transition'), { status: 400 });
  }
  
  await sr.save();
  return sr;
}

export async function listUserRequests(userId, { status, from, to } = {}) {
  const query = { clientId: userId };
  if (status) query.status = status;
  if (from || to) {
    query.createdAt = {};
    if (from) query.createdAt.$gte = new Date(from);
    if (to) query.createdAt.$lte = new Date(to);
  }
  return ServiceRequest.find(query).sort({ createdAt: -1 });
}

export async function listProviderCompleted(providerId, { from, to } = {}) {
  const query = { providerId, status: 'completed' };
  if (from || to) {
    query.createdAt = {};
    if (from) query.createdAt.$gte = new Date(from);
    if (to) query.createdAt.$lte = new Date(to);
  }
  return ServiceRequest.find(query).sort({ createdAt: -1 });
}

// Nouvelle fonction de recherche par critères
export async function searchServicesByCriteria({ q, category, minBudget, maxBudget }) {
  const query = { status: 'pending' };
  
  if (q) {
    query.$or = [
      { title: { $regex: q, $options: 'i' } },
      { description: { $regex: q, $options: 'i' } }
    ];
  }
  
  if (category) {
    query.category = category;
  }
  
  if (minBudget || maxBudget) {
    query.budget = {};
    if (minBudget) query.budget.$gte = parseFloat(minBudget);
    if (maxBudget) query.budget.$lte = parseFloat(maxBudget);
  }
  
  return ServiceRequest.find(query)
    .populate('clientId', 'firstName lastName')
    .sort({ createdAt: -1 });
}

// Nouvelle fonction pour les services à proximité
export async function getServicesNearLocation(latitude, longitude, radiusKm) {
  const radiusInMeters = radiusKm * 1000;
  
  return ServiceRequest.find({
    status: 'pending',
    'location.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude] // [lng, lat] pour MongoDB
        },
        $maxDistance: radiusInMeters
      }
    }
  })
  .populate('clientId', 'firstName lastName')
  .sort({ createdAt: -1 });
} 