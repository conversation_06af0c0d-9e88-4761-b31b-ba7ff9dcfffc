import { Router } from 'express';
import { validate } from '../middlewares/validate.js';
import { signupSchema, loginSchema } from '../utils/joiSchemas.js';
import { signup, login, googleAuth, googleCallback, facebookAuth, facebookCallback, refresh, logout, requestReset, doReset } from '../controllers/authController.js';
import { uploadSinglePhoto, handleUploadError } from '../middlewares/upload.js';
import { authenticate } from '../middlewares/auth.js';

const router = Router();

/**
 * @openapi
 * /api/v1/auth/signup:
 *   post:
 *     summary: Sign up with email or phone
 *     requestBody:
 *       required: true
 *     responses:
 *       201:
 *         description: Created
 */
router.post('/signup', validate(signupSchema), signup);

/**
 * @openapi
 * /api/v1/auth/login:
 *   post:
 *     summary: Login with email or phone
 *     responses:
 *       200:
 *         description: OK
 */
router.post('/login', validate(loginSchema), login);

router.get('/google', googleAuth);
router.get('/google/callback', googleCallback);
router.get('/facebook', facebookAuth);
router.get('/facebook/callback', facebookCallback);

router.post('/refresh', refresh);
router.post('/logout', logout);
router.post('/password/request-reset', requestReset);
router.post('/password/reset', doReset);

// 🚀 Route pour ajouter le champ name à un utilisateur spécifique
router.post('/add-name/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { name } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Le champ name est requis'
      });
    }
    
    const User = (await import('../models/User.js')).default;
    
    // Mettre à jour l'utilisateur avec le nom
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { name },
      { new: true, runValidators: true }
    );
    
    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    res.json({
      success: true,
      message: 'Nom ajouté avec succès',
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        phone: updatedUser.phone
      }
    });
    
  } catch (error) {
    console.error('Erreur lors de l\'ajout du nom:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout du nom',
      error: error.message
    });
  }
});

// 🚀 Route pour mettre à jour la photo de profil
router.post('/update-profile-photo', authenticate, uploadSinglePhoto, handleUploadError, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Aucune photo n\'a été fournie'
      });
    }

    console.log('📸 Fichier reçu:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      buffer: req.file.buffer ? 'Buffer présent' : 'Buffer manquant'
    });

    const User = (await import('../models/User.js')).default;
    
    // Convertir le buffer en base64
    const base64Photo = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;
    
    console.log('🔄 Photo convertie en base64, taille:', base64Photo.length, 'caractères');
    
    // Mise à jour de l'utilisateur avec la nouvelle photo
    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      { 
        profilePhoto: base64Photo,
        avatarUrl: undefined // Effacer l'ancien avatarUrl si existant
      },
      { new: true, runValidators: true }
    );
    
    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    res.json({
      success: true,
      message: 'Photo de profil mise à jour avec succès',
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        phone: updatedUser.phone,
        profilePhoto: updatedUser.profilePhoto ? 'Photo présente' : 'Aucune photo',
        avatarUrl: updatedUser.avatarUrl,
        role: updatedUser.role,
        averageRating: updatedUser.averageRating,
        totalRatings: updatedUser.totalRatings
      }
    });
    
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour de la photo de profil:', {
      message: error.message || 'Message d\'erreur manquant',
      stack: error.stack || 'Stack trace manquant',
      name: error.name || 'Nom d\'erreur manquant',
      fullError: error
    });
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la photo de profil',
      error: error.message || 'Erreur inconnue'
    });
  }
});

// 🚀 Route pour récupérer le profil utilisateur
router.get('/profile', authenticate, async (req, res) => {
  try {
    const User = (await import('../models/User.js')).default;
    
    const user = await User.findById(req.user.id).select('-passwordHash -refreshToken -resetPasswordToken -resetPasswordExpires');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        profilePhoto: user.profilePhoto || null,
        avatarUrl: user.avatarUrl,
        role: user.role,
        averageRating: user.averageRating,
        totalRatings: user.totalRatings,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
    
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du profil',
      error: error.message
    });
  }
});

// 🚀 Route pour supprimer la photo de profil
router.delete('/profile-photo', authenticate, async (req, res) => {
  try {
    const User = (await import('../models/User.js')).default;
    
    // Mise à jour de l'utilisateur en supprimant la photo
    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      { 
        profilePhoto: undefined,
        avatarUrl: undefined
      },
      { new: true, runValidators: true }
    );
    
    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    res.json({
      success: true,
      message: 'Photo de profil supprimée avec succès',
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        phone: updatedUser.phone,
        profilePhoto: updatedUser.profilePhoto ? 'Photo présente' : 'Aucune photo',
        avatarUrl: updatedUser.avatarUrl,
        role: updatedUser.role,
        averageRating: updatedUser.averageRating,
        totalRatings: updatedUser.totalRatings
      }
    });
    
  } catch (error) {
    console.error('❌ Erreur lors de la suppression de la photo de profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la photo de profil',
      error: error.message
    });
  }
});

export default router; 