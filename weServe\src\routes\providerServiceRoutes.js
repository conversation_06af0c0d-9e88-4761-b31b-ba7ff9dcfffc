import express from 'express';
import { authenticate } from '../middlewares/auth.js';
import { uploadPhotos } from '../middlewares/upload.js';
import * as providerServiceController from '../controllers/providerServiceController.js';

const router = express.Router();

// Routes pour les services des prestataires

// POST /api/v1/provider-services - Créer un nouveau service (PRESTATAIRE)
router.post('/', 
  authenticate, 
  uploadPhotos, // Déjà configuré pour 5 photos max
  providerServiceController.createProviderService
);

// GET /api/v1/provider-services - Lister tous les services actifs (TOUS)
router.get('/', 
  authenticate, 
  providerServiceController.getAllActiveServices
);

// GET /api/v1/provider-services/search - Rechercher des services (TOUS)
router.get('/search', 
  authenticate, 
  providerServiceController.searchServices
);

// GET /api/v1/provider-services/nearby - Services à proximité (TOUS)
router.get('/nearby', 
  authenticate, 
  providerServiceController.getNearbyServices
);

// GET /api/v1/provider-services/my-services - Mes services (PRESTATAIRE)
router.get('/my-services', 
  authenticate, 
  providerServiceController.getMyServices
);

// GET /api/v1/provider-services/stats - Statistiques de mes services (PRESTATAIRE)
router.get('/stats', 
  authenticate, 
  providerServiceController.getMyServiceStats
);

// 🧪 GET /api/v1/provider-services/debug/populate - Diagnostic du populate (ADMIN)
router.get('/debug/populate', 
  authenticate, 
  async (req, res) => {
    try {
      const { debugPopulateIssue } = await import('../services/providerServiceService.js');
      const result = await debugPopulateIssue();
      res.json(result);
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
);

// 🧪 GET /api/v1/provider-services/debug/test-populate - Test simple du populate
router.get('/debug/test-populate', 
  authenticate, 
  async (req, res) => {
    try {
      const ProviderService = (await import('../models/ProviderService.js')).default;
      
      // Test simple du populate
      const service = await ProviderService.findOne({ status: 'active' })
        .populate('providerId', 'name email phone avatarUrl averageRating totalRatings');
      
      if (!service) {
        return res.status(404).json({ message: 'Aucun service trouvé' });
      }
      
      res.json({
        success: true,
        serviceId: service._id,
        providerId: service.providerId,
        providerName: service.providerId?.name,
        providerEmail: service.providerId?.email,
        providerPhone: service.providerId?.phone
      });
      
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
);

// GET /api/v1/provider-services/:id - Obtenir un service par ID (TOUS)
router.get('/:id', 
  authenticate, 
  providerServiceController.getProviderServiceById
);

// PUT /api/v1/provider-services/:id - Mettre à jour un service (PRESTATAIRE)
router.put('/:id', 
  authenticate, 
  uploadPhotos, // Déjà configuré pour 5 photos max
  providerServiceController.updateProviderService
);

// DELETE /api/v1/provider-services/:id - Supprimer un service (PRESTATAIRE)
router.delete('/:id', 
  authenticate, 
  providerServiceController.deleteProviderService
);

export default router;
