import multer from 'multer';
import { createServiceRequest as createService, listOpenRequests, getRequestById, updateStatus, listUserRequests, searchServicesByCriteria, getServicesNearLocation } from '../services/serviceRequestService.js';
import ServiceRequest from '../models/ServiceRequest.js';

const upload = multer({ storage: multer.memoryStorage(), limits: { files: 5, fileSize: 10 * 1024 * 1024 } }); // Augmenté à 10MB
export const uploadPhotos = upload.array('photos', 5);

// Créer une nouvelle demande de service
export async function create(req, res, next) {
  try {
    // Vérifier que l'utilisateur est un client
    if (req.user.role !== 'client') {
      return res.status(403).json({
        success: false,
        error: 'Seuls les clients peuvent créer des demandes de service'
      });
    }

    const files = req.files || [];
    const photos = [];
    
    // Convertir les photos en base64
    for (const file of files) {
      const base64Photo = `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;
      photos.push(base64Photo);
    }
    
    console.log(`📸 ${photos.length} photos converties en base64`);

    const serviceRequestData = {
      ...req.body,
      photos: photos,
      clientId: req.user.id
    };

    const serviceRequest = await createService(serviceRequestData);

    res.status(201).json({
      success: true,
      data: {
        id: serviceRequest._id,
        title: serviceRequest.title,
        status: serviceRequest.status,
        createdAt: serviceRequest.createdAt
      }
    });

  } catch (err) {
    next(err);
  }
}

// Accepter une demande de service
export async function acceptServiceRequest(req, res, next) {
  try {
    const { id } = req.params;
    const { message } = req.body;

    // Vérifier que l'utilisateur est un prestataire
    if (req.user.role !== 'provider') {
      return res.status(403).json({
        success: false,
        error: 'Seuls les prestataires peuvent accepter des demandes de service'
      });
    }

    const serviceRequest = await ServiceRequest.findById(id);
    
    if (!serviceRequest) {
      return res.status(404).json({
        success: false,
        error: 'Demande de service non trouvée'
      });
    }

    // Vérifier que la demande est en attente
    if (serviceRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: 'Cette demande ne peut plus être acceptée'
      });
    }

    // Vérifier que le prestataire n'a pas déjà accepté
    if (serviceRequest.hasProviderAccepted(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Vous avez déjà accepté cette demande'
      });
    }

    // Ajouter le prestataire
    serviceRequest.addProvider(req.user.id, message);
    await serviceRequest.save();

    res.json({
      success: true,
      message: 'Demande acceptée avec succès',
      data: {
        serviceRequestId: serviceRequest._id,
        providerId: req.user.id,
        acceptedAt: new Date()
      }
    });

  } catch (err) {
    if (err.message.includes('ne peut plus accepter') || 
        err.message.includes('a déjà accepté')) {
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }
    next(err);
  }
}

// Choisir un prestataire
export async function chooseProvider(req, res, next) {
  try {
    const { id } = req.params;
    const { providerId } = req.body;

    if (!providerId) {
      return res.status(400).json({
        success: false,
        error: 'providerId est requis'
      });
    }

    const serviceRequest = await ServiceRequest.findById(id);
    
    if (!serviceRequest) {
      return res.status(404).json({
        success: false,
        error: 'Demande de service non trouvée'
      });
    }

    // Vérifier que l'utilisateur est le client qui a créé la demande
    if (serviceRequest.clientId.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Vous ne pouvez choisir un prestataire que pour vos propres demandes'
      });
    }

    // Vérifier que la demande peut être choisie
    if (!serviceRequest.canChooseProvider) {
      return res.status(400).json({
        success: false,
        error: 'Cette demande ne peut pas être choisie actuellement'
      });
    }

    // Vérifier que le prestataire a bien accepté
    if (!serviceRequest.hasProviderAccepted(providerId)) {
      return res.status(400).json({
        success: false,
        error: 'Ce prestataire n\'a pas accepté cette demande'
      });
    }

    // Choisir le prestataire
    serviceRequest.chooseProvider(providerId);
    await serviceRequest.save();

    res.json({
      success: true,
      message: 'Prestataire choisi avec succès',
      data: {
        serviceRequestId: serviceRequest._id,
        chosenProvider: providerId,
        status: serviceRequest.status,
        chosenAt: serviceRequest.chosenAt
      }
    });

  } catch (err) {
    if (err.message.includes('ne peut pas être choisie') || 
        err.message.includes('n\'a pas accepté')) {
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }
    next(err);
  }
}

// Obtenir une demande de service spécifique
export async function getServiceRequest(req, res, next) {
  try {
    const { id } = req.params;
    
    const serviceRequest = await ServiceRequest.findById(id)
      .populate('clientId', 'name email phone')
      .populate('providers.providerId', 'name email phone')
      .populate('chosenProvider', 'name email phone');

    if (!serviceRequest) {
      return res.status(404).json({
        success: false,
        error: 'Demande de service non trouvée'
      });
    }

    res.json({
      success: true,
      data: serviceRequest
    });

  } catch (err) {
    next(err);
  }
}

// Lister les demandes de service
export async function listServiceRequests(req, res, next) {
  try {
    const { status, category, minBudget, maxBudget, page = 1, limit = 10 } = req.query;
    
    const filter = {};
    if (status) filter.status = status;
    if (category) filter.category = category;
    if (minBudget || maxBudget) {
      filter.budget = {};
      if (minBudget) filter.budget.$gte = parseFloat(minBudget);
      if (maxBudget) filter.budget.$lte = parseFloat(maxBudget);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [serviceRequests, total] = await Promise.all([
      ServiceRequest.find(filter)
        .populate('clientId', 'name')
        .populate('providers.providerId', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      ServiceRequest.countDocuments(filter)
    ]);

    res.json({
      success: true,
      data: serviceRequests,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (err) {
    next(err);
  }
}

// Mettre à jour le statut d'une demande
export async function updateServiceRequestStatus(req, res, next) {
  try {
    const { id } = req.params;
    const { status, reason } = req.body;

    const serviceRequest = await ServiceRequest.findById(id);
    
    if (!serviceRequest) {
      return res.status(404).json({
        success: false,
        error: 'Demande de service non trouvée'
      });
    }

    // Vérifier les permissions
    const canUpdate = 
      req.user.role === 'client' && serviceRequest.clientId.toString() === req.user.id ||
      req.user.role === 'provider' && serviceRequest.chosenProvider?.toString() === req.user.id;

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        error: 'Vous n\'avez pas la permission de modifier cette demande'
      });
    }

    // Validation des transitions de statut
    const validTransitions = {
      'pending': ['accepted', 'cancelled'],
      'accepted': ['in_progress', 'cancelled'],
      'in_progress': ['completed', 'cancelled'],
      'completed': [],
      'cancelled': []
    };

    if (!validTransitions[serviceRequest.status].includes(status)) {
      return res.status(400).json({
        success: false,
        error: `Transition de statut invalide: ${serviceRequest.status} → ${status}`
      });
    }

    // Mise à jour du statut
    serviceRequest.status = status;
    
    if (status === 'cancelled' && reason) {
      serviceRequest.cancellationReason = reason;
    }

    await serviceRequest.save();

    res.json({
      success: true,
      message: 'Statut mis à jour avec succès',
      data: {
        id: serviceRequest._id,
        status: serviceRequest.status,
        updatedAt: serviceRequest.updatedAt
      }
    });

  } catch (err) {
    next(err);
  }
}

// Fonctions existantes (garder pour compatibilité)
export async function listOpen(_req, res, next) {
  try {
    const list = await listOpenRequests();
    res.json(list);
  } catch (err) { next(err); }
}

export async function getById(req, res, next) {
  try {
    const item = await getRequestById(req.params.id);
    if (!item) return res.status(404).json({ message: 'Not found' });
    res.json(item);
  } catch (err) { next(err); }
}

export async function changeStatus(req, res, next) {
  try {
    const item = await updateStatus({ id: req.params.id, status: req.body.status, user: req.user });
    res.json(item);
  } catch (err) { next(err); }
}

// Nouvelle fonction de recherche
export async function searchServices(req, res, next) {
  try {
    const { q, category, minBudget, maxBudget } = req.query;
    const services = await searchServicesByCriteria({ q, category, minBudget, maxBudget });
    res.json({
      success: true,
      data: services
    });
  } catch (err) { next(err); }
}

// Nouvelle fonction pour obtenir les catégories
export async function getCategories(req, res, next) {
  try {
    const categories = [
      'Plomberie', 'Électricité', 'Ménage', 'Jardinage', 
      'Déménagement', 'Peinture', 'Réparation', 'Installation', 'Autre'
    ];
    res.json({
      success: true,
      data: categories
    });
  } catch (err) { next(err); }
}

// Nouvelle fonction pour les services à proximité
export async function getNearbyServices(req, res, next) {
  try {
    const { latitude, longitude, radius = 10 } = req.query; // radius en km
    const services = await getServicesNearLocation(parseFloat(latitude), parseFloat(longitude), parseFloat(radius));
    res.json({
      success: true,
      data: services
    });
  } catch (err) { next(err); }
}

// Nouvelle fonction pour obtenir les services de l'utilisateur connecté
export async function getMyServices(req, res, next) {
  try {
    const { status, from, to } = req.query;
    const services = await listUserRequests(req.user.id, { status, from, to });
    res.json({
      success: true,
      data: services
    });
  } catch (err) { next(err); }
} 