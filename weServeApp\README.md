# WeServe - Application Mobile React Native

WeServe est une plateforme mobile qui connecte les clients avec des prestataires de services locaux. L'application permet aux utilisateurs de créer des demandes de services ou d'offrir leurs compétences en tant que prestataires.

## 🚀 Fonctionnalités

### Pour les Clients
- ✅ Création de demandes de services
- ✅ Recherche et filtrage des prestataires
- ✅ Suivi des demandes en temps réel
- ✅ Évaluation des prestataires
- ✅ Historique des services

### Pour les Prestataires
- ✅ Création de profils de services
- ✅ Gestion des demandes reçues
- ✅ Tableau de bord avec statistiques
- ✅ Planification des interventions
- ✅ Gestion des revenus

### Fonctionnalités Générales
- ✅ Authentification sécurisée
- ✅ Interface responsive et moderne
- ✅ Navigation intuitive
- ✅ Gestion des rôles (client/prestataire)
- ✅ Support multilingue (français)

## 🛠️ Technologies Utilisées

- **Framework** : React Native avec Expo SDK 53
- **Langage** : JavaScript
- **Gestion d'état** : Zustand
- **Navigation** : React Navigation 6
- **UI Components** : React Native Elements
- **Icônes** : Expo Vector Icons
- **Gradients** : React Native Linear Gradient
- **Stockage** : AsyncStorage

## 📱 Écrans de l'Application

### Écrans d'Authentification
1. **LoginScreen** - Connexion utilisateur
2. **SignupScreen** - Inscription avec sélection de rôle

### Écrans Principaux
3. **HomeScreen** - Accueil avec actions rapides et catégories
4. **ServicesListScreen** - Liste des services/demandes avec filtres
5. **CreateServiceRequestScreen** - Création de demande (clients)
6. **CreateProviderServiceScreen** - Création de service (prestataires)
7. **ServiceDetailScreen** - Détails d'un service/demande
8. **DashboardScreen** - Tableau de bord avec statistiques
9. **ProfileScreen** - Profil utilisateur et paramètres

## 🏗️ Architecture

```
src/
├── components/          # Composants réutilisables
│   ├── common/         # Boutons, inputs, cards
│   ├── forms/          # Formulaires spécifiques
│   └── screens/        # Écrans de l'application
├── hooks/              # Hooks personnalisés
├── services/           # Services API et utilitaires
├── stores/             # Stores Zustand
├── navigation/         # Configuration de navigation
├── constants/          # Constantes et thème
└── utils/              # Fonctions utilitaires
```

## 🎨 Design System

### Couleurs
- **Primary** : #2563EB (Bleu principal)
- **Secondary** : #10B981 (Vert succès)
- **Accent** : #F59E0B (Orange accent)
- **Danger** : #EF4444 (Rouge erreur)
- **Warning** : #F97316 (Orange avertissement)
- **Success** : #22C55E (Vert succès)

### Typographie
- **Regular** : System (par défaut)
- **Medium** : System Medium
- **Bold** : System Bold

### Composants
- Boutons avec variantes (primary, secondary, outline, danger)
- Inputs avec validation et icônes
- Cards avec ombres et bordures arrondies
- Navigation par onglets et pile

## 🔐 Gestion d'État

### Stores Zustand
1. **authStore** - Authentification et profil utilisateur
2. **servicesStore** - Services et demandes
3. **locationStore** - Géolocalisation et distances

### Persistance
- Stockage local avec AsyncStorage
- Persistance automatique des données utilisateur
- Gestion des sessions

## 📦 Installation et Démarrage

### Prérequis
- Node.js (version 16 ou supérieure)
- npm ou yarn
- Expo CLI
- Android Studio (pour Android) ou Xcode (pour iOS)

### Installation
```bash
# Cloner le repository
git clone <repository-url>
cd weServeApp

# Installer les dépendances
npm install

# Démarrer l'application
npm start
```

### Scripts Disponibles
```bash
npm start          # Démarrer Expo
npm run android    # Lancer sur Android
npm run ios        # Lancer sur iOS
npm run web        # Lancer sur web
```

## 🔧 Configuration

### Variables d'Environnement
Créez un fichier `.env` à la racine du projet :
```env
API_BASE_URL=http://localhost:4000/api/v1
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### Permissions
L'application nécessite les permissions suivantes :
- **Camera** : Prise de photos
- **Location** : Géolocalisation
- **Storage** : Accès aux fichiers
- **Notifications** : Notifications push

## 📱 Compatibilité

- **iOS** : 12.0+
- **Android** : 6.0+ (API 23+)
- **Expo** : SDK 53+

## 🚧 Fonctionnalités à Implémenter

### Phase 2
- [ ] Intégration API backend
- [ ] Gestion des photos et upload
- [ ] Système de messagerie
- [ ] Paiements in-app
- [ ] Notifications push

### Phase 3
- [ ] Mode hors ligne
- [ ] Synchronisation des données
- [ ] Tests unitaires et d'intégration
- [ ] Analytics et monitoring
- [ ] Support multilingue

## 🧪 Tests

```bash
# Lancer les tests
npm test

# Tests avec coverage
npm run test:coverage
```

## 📊 Performance

- Lazy loading des composants
- Optimisation des images
- Gestion de la mémoire
- Cache des données

## 🔒 Sécurité

- Validation des entrées utilisateur
- Chiffrement des données sensibles
- Gestion sécurisée des tokens
- Protection contre les injections

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou problème :
- Ouvrir une issue sur GitHub
- Contacter l'équipe de développement
- Consulter la documentation technique

## 🙏 Remerciements

- Équipe React Native
- Communauté Expo
- Contributeurs open source
- Testeurs et utilisateurs

---

**WeServe** - Connecter les talents, simplifier les services 🚀
