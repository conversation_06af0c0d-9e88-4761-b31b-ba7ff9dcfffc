import { jest, describe, test, expect, beforeEach } from '@jest/globals';

await jest.unstable_mockModule('../src/models/ServiceRequest.js', () => ({
  default: { create: jest.fn(), findById: jest.fn(), find: jest.fn() }
}));

const { default: ServiceRequest } = await import('../src/models/ServiceRequest.js');
const srs = await import('../src/services/serviceRequestService.js');

describe('serviceRequestService', () => {
  beforeEach(() => jest.resetAllMocks());

  test('createServiceRequest stores data', async () => {
    const mock = { _id: 's1' };
    ServiceRequest.create.mockResolvedValue(mock);
    const res = await srs.createServiceRequest({ title: 't', description: 'desc long text', photos: [], lat: 1, lng: 2, date: new Date(), budget: 100, clientId: 'u1' });
    expect(res).toBe(mock);
  });

  test('updateStatus accepts when provider', async () => {
    const sr = { _id: 's1', status: 'pending', save: jest.fn(), providerId: null };
    ServiceRequest.findById.mockResolvedValue(sr);
    const res = await srs.updateStatus({ id: 's1', status: 'accepted', user: { id: 'p1', role: 'provider' } });
    expect(sr.status).toBe('accepted');
    expect(sr.providerId).toBe('p1');
    expect(res).toBe(sr);
  });
}); 