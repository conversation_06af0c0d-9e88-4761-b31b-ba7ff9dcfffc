import { Platform } from 'react-native';
import { expoConfig, topInsetsConfig } from '../utils/expoConfig';
import { getSafeConfiguration, disableProblematicFeatures } from '../utils/disableProblematicFeatures';

// Configuration principale de l'application
export const appConfig = {
  // Configuration générale
  general: {
    name: 'WeServe',
    version: '1.0.0',
    environment: __DEV__ ? 'development' : 'production',
  },
  
  // Configuration Expo
  expo: {
    ...expoConfig,
    // Configuration spécifique pour éviter l'erreur topInsetsChange
    topInsets: topInsetsConfig,
  },
  
  // Configuration de la navigation
  navigation: {
    // Désactiver les fonctionnalités problématiques
    enableScreens: false,
    
    // Configuration des transitions
    transitions: {
      // Utiliser des transitions simples
      cardStyleInterpolator: ({ current, layouts }) => ({
        cardStyle: {
          transform: [
            {
              translateX: current.progress.interpolate({
                inputRange: [0, 1],
                outputRange: [layouts.screen.width, 0],
              }),
            },
          ],
        },
      }),
    },
    
    // Configuration des écrans
    screenOptions: {
      headerShown: false,
      gestureEnabled: false,
      // Désactiver les animations problématiques
      animationEnabled: false,
    },
  },
  
  // Configuration des onglets
  tabs: {
    screenOptions: {
      headerShown: false,
      // Configuration des onglets
      tabBarStyle: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
        height: Platform.OS === 'ios' ? 88 : 60,
        paddingBottom: Platform.OS === 'ios' ? 20 : 8,
        paddingTop: 8,
      },
      tabBarActiveTintColor: '#2563EB',
      tabBarInactiveTintColor: '#6B7280',
      tabBarLabelStyle: {
        fontSize: 12,
        fontWeight: '500',
      },
    },
  },
  
  // Configuration des thèmes
  theme: {
    colors: {
      primary: '#2563EB',
      secondary: '#10B981',
      accent: '#F59E0B',
      danger: '#EF4444',
      warning: '#F97316',
      success: '#22C55E',
      dark: '#1F2937',
      light: '#F9FAFB',
      gray: '#6B7280',
      border: '#E5E7EB',
    },
    sizes: {
      base: 8,
      small: 12,
      font: 14,
      medium: 16,
      large: 18,
      extraLarge: 24,
      padding: 16,
      radius: 8,
    },
  },
};

// Fonction pour initialiser la configuration
export const initializeConfig = () => {
  // Désactiver les fonctionnalités problématiques
  disableProblematicFeatures();
  
  // Configuration spécifique selon la plateforme
  if (Platform.OS === 'android') {
    // Désactiver complètement les fonctionnalités problématiques sur Android
    appConfig.navigation.enableScreens = false;
    appConfig.navigation.screenOptions.animationEnabled = false;
    appConfig.navigation.screenOptions.gestureEnabled = false;
  }
  
  return appConfig;
};

// Configuration pour éviter l'erreur topInsetsChange
export const getTopInsetsSafeConfig = () => {
  // Utiliser la configuration sécurisée
  const safeConfig = getSafeConfiguration();
  
  return {
    ...appConfig,
    navigation: {
      ...appConfig.navigation,
      ...safeConfig,
    },
  };
};

export default appConfig;
