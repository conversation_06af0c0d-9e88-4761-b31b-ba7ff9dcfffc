import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      userRole: null, // 'client' ou 'provider'
      isLoading: false,
      error: null,
      
      login: (userData, token) => set({
        user: userData,
        token,
        isAuthenticated: true,
        userRole: userData.role,
        isLoading: false,
        error: null
      }),
      
      logout: () => set({
        user: null,
        token: null,
        isAuthenticated: false,
        userRole: null,
        isLoading: false,
        error: null
      }),
      
      updateProfile: (userData) => set({ 
        user: { ...get().user, ...userData } 
      }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error, isLoading: false }),
      
      clearError: () => set({ error: null })
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage)
    }
  )
);
