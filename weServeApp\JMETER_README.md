# 🧪 Tests JMeter pour weServeApp

## 📋 **Vue d'ensemble**
Ce dossier contient tous les fichiers nécessaires pour tester les performances de votre application weServeApp avec Apache JMeter.

## 🚀 **Installation et Configuration**

### **1. Installer JMeter**
```bash
# Option 1: Téléchargement manuel
# Visitez: https://jmeter.apache.org/download_jmeter.cgi
# Téléchargez la dernière version stable

# Option 2: Avec Chocolatey (Windows)
choco install jmeter

# Option 3: Avec Homebrew (macOS)
brew install jmeter
```

### **2. Vérifier l'Installation**
```bash
jmeter --version
```

### **3. Préparer l'Environnement**
- ✅ Backend weServeApp démarré sur le port 4000
- ✅ Base de données accessible
- ✅ CORS configuré correctement
- ✅ Variables d'environnement définies

## 📁 **Structure des Fichiers**

```
weServeApp/
├── JMETER_TEST_PLAN.md          # Guide complet des tests
├── weServeApp_TestPlan.jmx      # Plan de test JMeter
├── run_jmeter_tests.bat         # Script Windows (CMD)
├── run_jmeter_tests.ps1         # Script Windows (PowerShell)
├── JMETER_README.md             # Ce fichier
└── jmeter_results/              # Dossier des résultats (créé automatiquement)
```

## 🎯 **Types de Tests Disponibles**

### **1. Test de Fumée (Smoke Test)**
- **Utilisateurs** : 1
- **Durée** : ~30 secondes
- **Objectif** : Validation de la configuration
- **Utilisation** : Vérification rapide que tout fonctionne

### **2. Test de Charge (Load Test)**
- **Utilisateurs** : 10
- **Durée** : ~2 minutes
- **Objectif** : Test de performance normale
- **Utilisation** : Validation des performances en conditions normales

### **3. Test de Stress (Stress Test)**
- **Utilisateurs** : 50
- **Durée** : ~5 minutes
- **Objectif** : Test des limites de performance
- **Utilisation** : Identification des goulots d'étranglement

### **4. Test Personnalisé**
- **Utilisateurs** : Configurable
- **Durée** : Configurable
- **Ramp-up** : Configurable
- **Objectif** : Tests spécifiques selon vos besoins

## 🚀 **Exécution des Tests**

### **Option 1: Scripts Automatisés (Recommandé)**

#### **Windows CMD**
```bash
# Double-cliquez sur le fichier
run_jmeter_tests.bat

# Ou exécutez depuis la ligne de commande
run_jmeter_tests.bat
```

#### **Windows PowerShell**
```powershell
# Exécutez avec PowerShell
.\run_jmeter_tests.ps1

# Ou avec la politique d'exécution appropriée
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\run_jmeter_tests.ps1
```

### **Option 2: Ligne de Commande Directe**

#### **Test de Fumée**
```bash
jmeter -n -t weServeApp_TestPlan.jmx -l jmeter_results/smoke_test_results.jtl -e -o jmeter_results/smoke_test_report
```

#### **Test de Charge**
```bash
jmeter -n -t weServeApp_TestPlan.jmx -l jmeter_results/load_test_results.jtl -e -o jmeter_results/load_test_report
```

#### **Test de Stress**
```bash
jmeter -n -t weServeApp_TestPlan.jmx -l jmeter_results/stress_test_results.jtl -e -o jmeter_results/stress_test_report
```

## 📊 **Analyse des Résultats**

### **1. Rapports HTML**
- **Emplacement** : `jmeter_results/[test_type]_test_report/`
- **Contenu** : Graphiques, tableaux, métriques détaillées
- **Ouverture** : Ouvrez `index.html` dans votre navigateur

### **2. Fichiers de Logs**
- **Format** : `.jtl` (JMeter Test Log)
- **Contenu** : Données brutes des tests
- **Utilisation** : Import dans JMeter GUI pour analyse détaillée

### **3. Métriques Clés**
- **Temps de réponse** : P50, P90, P95, P99
- **Débit** : Requêtes par seconde
- **Taux d'erreur** : Pourcentage d'erreurs
- **Utilisation des ressources** : CPU, mémoire, réseau

## 🔧 **Configuration Avancée**

### **1. Modifier le Plan de Test**
- Ouvrez `weServeApp_TestPlan.jmx` dans JMeter GUI
- Modifiez les paramètres selon vos besoins
- Sauvegardez les modifications

### **2. Variables d'Environnement**
```bash
# Développement
baseUrl = http://localhost:4000/api/v1

# Production
baseUrl = https://your-production-api.com/api/v1
```

### **3. Données de Test**
- **Utilisateurs** : Créez des comptes de test dans votre base de données
- **Services** : Ajoutez des services de test
- **Demandes** : Créez des demandes de test

## 🚨 **Dépannage**

### **Erreurs Communes**

#### **1. "JMeter n'est pas reconnu"**
```bash
# Ajoutez JMeter au PATH système
# Ou utilisez le chemin complet
C:\apache-jmeter-5.6.3\bin\jmeter.bat
```

#### **2. "Connection refused"**
- ✅ Vérifiez que le backend est démarré
- ✅ Vérifiez le port 4000
- ✅ Vérifiez la configuration CORS

#### **3. "Authentication failed"**
- ✅ Vérifiez que les tokens JWT sont valides
- ✅ Vérifiez la configuration des headers
- ✅ Vérifiez que l'API d'authentification fonctionne

#### **4. "Timeout"**
- ✅ Augmentez les timeouts dans JMeter
- ✅ Vérifiez la performance du backend
- ✅ Vérifiez la base de données

### **Optimisations**

#### **1. Performance JMeter**
```bash
# Augmentez la mémoire heap
set HEAP=-Xms1g -Xmx4g

# Utilisez le mode non-GUI pour les tests
jmeter -n -t test.jmx
```

#### **2. Configuration Backend**
- **Pool de connexions** : Optimisez la taille
- **Cache** : Activez le cache des réponses
- **Compression** : Activez la compression gzip
- **CDN** : Utilisez un CDN pour les assets statiques

## 📚 **Ressources Supplémentaires**

### **Documentation Officielle**
- [Site officiel JMeter](https://jmeter.apache.org/)
- [Guide utilisateur](https://jmeter.apache.org/usermanual/index.html)
- [Bonnes pratiques](https://jmeter.apache.org/usermanual/best-practices.html)

### **Tutoriels et Exemples**
- [Tests d'API REST](https://jmeter.apache.org/usermanual/build-adv-web-test-plan.html)
- [Gestion de l'authentification](https://jmeter.apache.org/usermanual/component_reference.html#HTTP_Header_Manager)
- [Extraction de données](https://jmeter.apache.org/usermanual/component_reference.html#Regular_Expression_Extractor)

### **Outils Complémentaires**
- **Grafana** : Visualisation des métriques
- **InfluxDB** : Stockage des données de performance
- **Postman** : Tests d'API individuels
- **New Relic** : Monitoring en production

## 🎯 **Prochaines Étapes**

1. **Installez JMeter** sur votre système
2. **Vérifiez que votre backend** est démarré et accessible
3. **Exécutez un test de fumée** pour valider la configuration
4. **Lancez des tests de charge** pour identifier les goulots d'étranglement
5. **Analysez les résultats** et optimisez votre application
6. **Mettez en place le monitoring** en production

## 📞 **Support**

Si vous rencontrez des problèmes :

1. **Vérifiez la configuration** selon ce guide
2. **Consultez les logs** dans le dossier `jmeter_results/`
3. **Vérifiez la documentation** JMeter officielle
4. **Testez avec Postman** pour valider les endpoints individuellement

---

**🚀 Votre application weServeApp est maintenant prête pour des tests de performance professionnels !**

**Bon testing ! 🧪✨** 