import ProviderService from '../models/ProviderService.js';

// Créer un nouveau service de prestataire
export const createProviderService = async (serviceData, photos = []) => {
  try {
    // Convertir les photos en base64
    let photoData = [];
    if (photos && photos.length > 0) {
      photoData = photos.map(photo => `data:${photo.mimetype};base64,${photo.buffer.toString('base64')}`);
    }

    console.log(`📸 ${photoData.length} photos converties en base64 pour le service prestataire`);

    // Créer le service avec les photos en base64
    const providerService = await ProviderService.create({
      ...serviceData,
      photos: photoData
    });

    return providerService;
  } catch (error) {
    throw error;
  }
};

// Obtenir tous les services actifs (pour les clients)
export const getAllActiveServices = async (filters = {}) => {
  try {
    const { category, maxPrice, location, page = 1, limit = 20 } = filters;
    
    let query = { status: 'active' };
    
    if (category) {
      query.category = category;
    }
    
    if (maxPrice) {
      query.price = { $lte: maxPrice };
    }
    
    if (location && location.latitude && location.longitude) {
      const radius = location.radius || 10;
      query['location.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [location.longitude, location.latitude]
          },
          $maxDistance: radius * 1000
        }
      };
    }
    
    const skip = (page - 1) * limit;
    
    // Populate simplifié pour tester
    const services = await ProviderService.find(query)
      .populate('providerId', 'name email phone avatarUrl averageRating totalRatings')
      .sort({ rating: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    // Log de débogage pour vérifier le populate
    console.log('🔍 Debug populate - Premier service:', JSON.stringify(services[0]?.providerId, null, 2));
    
    const total = await ProviderService.countDocuments(query);
    
    return {
      services,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

// Obtenir un service par ID
export const getProviderServiceById = async (serviceId) => {
  try {
    const service = await ProviderService.findById(serviceId)
      .populate({
        path: 'providerId',
        select: 'name email phone avatarUrl averageRating totalRatings',
        model: 'User'
      })
      .populate('reviews');
    
    if (!service) {
      throw new Error('Service non trouvé');
    }
    
    // Log de débogage
    console.log('🔍 Debug populate by ID - Provider:', JSON.stringify(service.providerId, null, 2));
    
    return service;
  } catch (error) {
    throw error;
  }
};

// Obtenir les services d'un prestataire spécifique
export const getServicesByProvider = async (providerId, status = null) => {
  try {
    let query = { providerId };
    
    if (status) {
      query.status = status;
    }
    
    const services = await ProviderService.find(query)
      .populate({
        path: 'providerId',
        select: 'name email phone avatarUrl averageRating totalRatings',
        model: 'User'
      })
      .sort({ createdAt: -1 });
    
    // Log de débogage
    if (services.length > 0) {
      console.log('🔍 Debug populate by provider - Premier service:', JSON.stringify(services[0]?.providerId, null, 2));
    }
    
    return services;
  } catch (error) {
    throw error;
  }
};

// Mettre à jour un service
export const updateProviderService = async (serviceId, updateData, userId) => {
  try {
    const service = await ProviderService.findById(serviceId);
    
    if (!service) {
      throw new Error('Service non trouvé');
    }
    
    // Vérifier que l'utilisateur est le propriétaire du service
    if (service.providerId.toString() !== userId) {
      throw new Error('Non autorisé à modifier ce service');
    }
    
    // Si des nouvelles photos sont fournies, les convertir en base64
    if (updateData.photos && updateData.photos.length > 0) {
      const photoData = updateData.photos.map(photo => `data:${photo.mimetype};base64,${photo.buffer.toString('base64')}`);
      updateData.photos = photoData;
      console.log(`📸 ${photoData.length} photos converties en base64 pour la mise à jour`);
    }
    
    const updatedService = await ProviderService.findByIdAndUpdate(
      serviceId,
      updateData,
      { new: true, runValidators: true }
    ).populate({
      path: 'providerId',
      select: 'name email phone avatarUrl averageRating totalRatings',
      model: 'User'
    });
    
    return updatedService;
  } catch (error) {
    throw error;
  }
};

// Supprimer un service
export const deleteProviderService = async (serviceId, userId) => {
  try {
    const service = await ProviderService.findById(serviceId);
    
    if (!service) {
      throw new Error('Service non trouvé');
    }
    
    // Vérifier que l'utilisateur est le propriétaire du service
    if (service.providerId.toString() !== userId) {
      throw new Error('Non autorisé à supprimer ce service');
    }
    
    await ProviderService.findByIdAndUpdate(serviceId, { status: 'deleted' });
    
    return { message: 'Service supprimé avec succès' };
  } catch (error) {
    throw error;
  }
};

// Rechercher des services par critères
export const searchServices = async (searchCriteria) => {
  try {
    const { q, category, maxPrice, location, page = 1, limit = 20 } = searchCriteria;
    
    let query = { status: 'active' };
    
    // Recherche textuelle
    if (q) {
      query.$or = [
        { title: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ];
    }
    
    // Filtre par catégorie
    if (category) {
      query.category = category;
    }
    
    // Filtre par prix maximum
    if (maxPrice) {
      query.price = { $lte: maxPrice };
    }
    
    // Filtre par localisation
    if (location && location.latitude && location.longitude) {
      const radius = location.radius || 10;
      query['location.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [location.longitude, location.latitude]
          },
          $maxDistance: radius * 1000
        }
      };
    }
    
    const skip = (page - 1) * limit;
    
    const services = await ProviderService.find(query)
      .populate({
        path: 'providerId',
        select: 'name email phone avatarUrl averageRating totalRatings',
        model: 'User'
      })
      .sort({ rating: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await ProviderService.countDocuments(query);
    
    return {
      services,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

// Obtenir les services à proximité
export const getNearbyServices = async (latitude, longitude, radius = 10, category = null) => {
  try {
    let query = {
      status: 'active',
      'location.coordinates': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [longitude, latitude]
          },
          $maxDistance: radius * 1000
        }
      }
    };
    
    if (category) {
      query.category = category;
    }
    
    const services = await ProviderService.find(query)
      .populate({
        path: 'providerId',
        select: 'name email phone avatarUrl averageRating totalRatings',
        model: 'User'
      })
      .sort({ rating: -1 })
      .limit(50);
    
    return services;
  } catch (error) {
    throw error;
  }
};

// Obtenir les statistiques des services d'un prestataire
export const getProviderServiceStats = async (providerId) => {
  try {
    const stats = await ProviderService.aggregate([
      { $match: { providerId: providerId } },
      {
        $group: {
          _id: null,
          totalServices: { $sum: 1 },
          activeServices: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          averageRating: { $avg: '$rating' },
          totalReviews: { $sum: { $size: '$reviews' } }
        }
      }
    ]);
    
    return stats[0] || {
      totalServices: 0,
      activeServices: 0,
      averageRating: 0,
      totalReviews: 0
    };
  } catch (error) {
    throw error;
  }
};

// 🧪 FONCTION DE TEST POUR DIAGNOSTIQUER LE POPULATE
export const debugPopulateIssue = async () => {
  try {
    console.log('🔍 === DÉBUT DU DIAGNOSTIC POPULATE ===');
    
    // 1. Vérifier un service sans populate
    const serviceWithoutPopulate = await ProviderService.findOne({ status: 'active' });
    console.log('📋 Service sans populate:', {
      id: serviceWithoutPopulate?._id,
      providerId: serviceWithoutPopulate?.providerId,
      providerIdType: typeof serviceWithoutPopulate?.providerId
    });
    
    if (serviceWithoutPopulate) {
      // 2. Vérifier l'utilisateur directement
      const User = (await import('../models/User.js')).default;
      const user = await User.findById(serviceWithoutPopulate.providerId);
      console.log('👤 Utilisateur trouvé directement:', {
        id: user?._id,
        name: user?.name,
        email: user?.email,
        phone: user?.phone,
        avatarUrl: user?.avatarUrl
      });
      
      // 3. Tester le populate manuellement
      const serviceWithPopulate = await ProviderService.findById(serviceWithoutPopulate._id)
        .populate({
          path: 'providerId',
          select: 'name email phone avatarUrl averageRating totalRatings',
          model: 'User'
        });
      
      console.log('🔗 Service avec populate:', {
        id: serviceWithPopulate?._id,
        providerId: serviceWithPopulate?.providerId,
        providerName: serviceWithPopulate?.providerId?.name,
        providerEmail: serviceWithPopulate?.providerId?.email
      });
      
      // 4. Vérifier la structure complète
      console.log('📊 Structure complète du populate:', JSON.stringify(serviceWithPopulate?.providerId, null, 2));
    }
    
    console.log('🔍 === FIN DU DIAGNOSTIC POPULATE ===');
    
    return { success: true, message: 'Diagnostic terminé, vérifiez les logs' };
  } catch (error) {
    console.error('❌ Erreur lors du diagnostic:', error);
    return { success: false, error: error.message };
  }
};
