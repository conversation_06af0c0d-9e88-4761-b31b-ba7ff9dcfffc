# 🖥️ Guide JMeter Interface Graphique pour weServeApp

## 🎯 **Vue d'ensemble**
Ce guide vous explique comment utiliser l'interface graphique de JMeter pour tester votre API weServeApp.

## 🚀 **Étape 1: Ouvrir JMeter**
```bash
# Dans votre dossier JMeter
.\bin\jmeter.bat
```

## 📁 **Étape 2: Ouvrir le Plan de Test**
1. **File → Open**
2. **Sélectionnez** `weServeApp_TestPlan_Simple.jmx`
3. **Cliquez sur "Open"**

## 🔍 **Étape 3: Comprendre la Structure**

### **Structure du Plan de Test**
```
Test Plan
└── weServeApp Tests (Thread Group)
    ├── Test Login (POST)
    ├── Test Categories (sans token)
    ├── Test API Docs
    └── Listeners
        ├── View Results Tree
        └── Summary Report
```

### **Détails des Tests**
1. **Test Login (POST)**
   - **URL** : `http://localhost:5000/api/v1/auth/login`
   - **Method** : POST
   - **Headers** : `Content-Type: application/json`
   - **Body** : `email=<EMAIL>&password=test123`

2. **Test Categories (sans token)**
   - **URL** : `http://localhost:5000/api/v1/services/categories`
   - **Method** : GET
   - **Headers** : Aucun (doit retourner 401)

3. **Test API Docs**
   - **URL** : `http://localhost:5000/api-docs`
   - **Method** : GET
   - **Headers** : Aucun

## 🧪 **Étape 4: Exécuter le Test**

### **Prérequis**
- ✅ Backend weServeApp démarré sur le port 5000
- ✅ Plan de test ouvert dans JMeter

### **Lancement**
1. **Cliquez sur le bouton vert ▶️** (Start) dans la barre d'outils
2. **Regardez les résultats** en temps réel

## 📊 **Étape 5: Analyser les Résultats**

### **View Results Tree**
- **Affiche** chaque requête individuellement
- **Montre** les détails de la réponse
- **Permet** de voir les headers, body, etc.

### **Summary Report**
- **Résumé** des performances
- **Temps** de réponse moyen
- **Taux** de succès/échec

## 🎯 **Résultats Attendus**

### **✅ Tests qui DOIVENT réussir :**
1. **Test Login (POST)** : 
   - Code 200 (si credentials valides)
   - Ou code 400/401 (si credentials invalides)

2. **Test API Docs** : 
   - Code 200 OK

### **❌ Tests qui DOIVENT échouer (c'est normal !) :**
1. **Test Categories (sans token)** : 
   - Code 401 Unauthorized
   - Message "Missing token"

## 🔧 **Étape 6: Modifier les Tests**

### **Changer les Credentials**
1. **Clic droit sur "Test Login (POST)"**
2. **Modifiez** les valeurs dans "Arguments"
3. **email** : votre email de test
4. **password** : votre mot de passe de test

### **Ajouter de Nouveaux Tests**
1. **Clic droit sur "weServeApp Tests"**
2. **Add → Sampler → HTTP Request**
3. **Configurez** l'URL, method, headers

## 📝 **Étape 7: Sauvegarder les Résultats**

### **Sauvegarder en Fichier**
1. **Clic droit sur "View Results Tree"**
2. **Configure**
3. **Browse** → Choisissez un fichier `.jtl`
4. **Relancez le test**

### **Exporter en HTML**
1. **Clic droit sur "Summary Report"**
2. **Configure**
3. **Browse** → Choisissez un fichier `.html`

## 🚨 **Dépannage**

### **Erreur "Connection refused"**
- ✅ Vérifiez que votre backend weServeApp est démarré
- ✅ Vérifiez le port 5000
- ✅ Testez dans votre navigateur : `http://localhost:5000/api-docs`

### **Erreur "401 Unauthorized" pour Categories**
- ✅ **C'est normal !** Votre API est sécurisée
- ✅ Cela confirme que l'authentification fonctionne

### **Test Login échoue**
- ✅ Vérifiez les credentials dans les "Arguments"
- ✅ Vérifiez que l'endpoint `/auth/login` accepte POST
- ✅ Vérifiez le format des données

## 🎉 **Étape 8: Tests Avancés**

### **Test avec Authentification**
1. **Ajoutez** un extracteur de token après le login
2. **Utilisez** le token pour tester les endpoints protégés
3. **Vérifiez** que les endpoints protégés fonctionnent avec le token

### **Test de Charge**
1. **Modifiez** "Number of Threads" dans le Thread Group
2. **Ajoutez** un "Ramp-up period" pour monter en charge progressivement
3. **Lancez** le test et observez les performances

## 📚 **Ressources Utiles**

### **Dans JMeter**
- **Help → What's New** : Nouveautés de la version
- **Help → Function Helper** : Fonctions disponibles
- **Help → Apache JMeter** : Documentation intégrée

### **Tests Rapides**
- **Bouton ▶️** : Lancer le test
- **Bouton ⏸️** : Pause
- **Bouton ⏹️** : Arrêter
- **Bouton 🔄** : Nettoyer les résultats

## 🎯 **Prochaines Étapes**

1. **Exécutez** le test simple pour valider la configuration
2. **Analysez** les résultats dans "View Results Tree"
3. **Modifiez** les tests selon vos besoins
4. **Ajoutez** de nouveaux endpoints à tester
5. **Créez** des tests de charge et de performance

---

**🚀 Maintenant vous pouvez utiliser l'interface graphique de JMeter pour tester votre API weServeApp !** 