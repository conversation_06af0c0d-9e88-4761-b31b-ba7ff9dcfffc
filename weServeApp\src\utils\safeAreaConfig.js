import { Platform } from 'react-native';

// Configuration pour résoudre les problèmes de safe area sur Expo SDK 52
export const safeAreaConfig = {
  // Désactiver les animations problématiques sur Android
  enableScreens: Platform.OS === 'ios',
  
  // Configuration spécifique pour éviter l'erreur topInsetsChange
  screenOptions: {
    headerShown: false,
    gestureEnabled: true,
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
  },
};

// Fonction pour gérer les safe areas de manière sécurisée
export const getSafeAreaInsets = () => {
  // Valeurs par défaut pour éviter les erreurs
  return {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  };
};
