import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  FlatList,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import Card from '../common/Card';
import Button from '../common/Button';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { SERVICE_CATEGORIES } from '../../constants/categories';
import { useAuthStore } from '../../stores/authStore';
import { useServicesStore } from '../../stores/servicesStore';
import servicesService from '../../services/servicesService';
import { FILTER_PRESETS, combineFilters } from '../../utils/filterUtils';

const { width } = Dimensions.get('window');

const ServicesListScreen = ({ navigation, route }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(route.params?.category || null);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState('relevance');
  const [activeTab, setActiveTab] = useState(0);
  
  const { userRole, user } = useAuthStore();
  const { services, serviceRequests, myServices, isLoading, filters, setFilters } = useServicesStore();

  useEffect(() => {
    loadData();
  }, [selectedCategory, selectedStatus]);

  // Recharger les données quand l'écran devient actif
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadData();
    });

    return unsubscribe;
  }, [navigation]);

  const loadData = async () => {
    try {
      console.log('🔄 Chargement des données pour le rôle:', userRole);
      
      if (userRole === 'client') {
        // Pour les clients : charger leurs demandes ET les services disponibles des prestataires
        console.log('📱 Appel API: getMyServiceRequests et getAllProviderServices');
        
        const filters = {};
        if (selectedCategory) filters.category = selectedCategory;
        if (selectedStatus) filters.status = selectedStatus;
        
        // Utiliser les filtres prédéfinis si aucun statut n'est sélectionné
        if (!selectedStatus) {
          filters.from = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 jours
        }
        
        const [requestsResult, servicesResult] = await Promise.all([
          servicesService.getMyServiceRequests(filters),
          servicesService.getAllProviderServices(filters)
        ]);
        
        console.log('📥 Réponse API getMyServiceRequests:', requestsResult);
        console.log('📥 Réponse API getAllProviderServices:', servicesResult);
        
        if (requestsResult.success) {
          useServicesStore.getState().setServiceRequests(requestsResult.data || []);
          console.log('✅ Demandes mises à jour dans le store:', requestsResult.data?.length || 0, 'demandes');
        }
        
        if (servicesResult.success) {
          useServicesStore.getState().setServices(servicesResult.data || []);
          console.log('✅ Services disponibles mis à jour dans le store:', servicesResult.data?.length || 0, 'services');
          
          // Debug: afficher la structure du premier service
          if (servicesResult.data && servicesResult.data.length > 0) {
            console.log('🔍 Structure du premier service:', JSON.stringify(servicesResult.data[0], null, 2));
            console.log('🔍 providerId:', servicesResult.data[0].providerId);
            console.log('🔍 provider:', servicesResult.data[0].provider);
            console.log('🔍 providerName:', servicesResult.data[0].providerName);
          }
        }
      } else {
        // Pour les prestataires : récupérer les demandes ouvertes ET leurs services
        console.log('📱 Appel API: getOpenServiceRequests et getMyProviderServices');
        
        // Charger les demandes ouvertes
        const filters = {};
        if (selectedCategory) filters.category = selectedCategory;
        
        const [requestsResult, servicesResult] = await Promise.all([
          servicesService.getOpenServiceRequests(filters),
          servicesService.getMyProviderServices()
        ]);
        
        console.log('📥 Réponse API getOpenServiceRequests:', requestsResult);
        console.log('📥 Réponse API getMyProviderServices:', servicesResult);
        
        if (requestsResult.success) {
          useServicesStore.getState().setServiceRequests(requestsResult.data || []);
          console.log('✅ Demandes ouvertes mises à jour dans le store:', requestsResult.data?.length || 0, 'demandes');
        }
        
        if (servicesResult.success) {
          useServicesStore.getState().setMyServices(servicesResult.data || []);
          console.log('✅ Services du prestataire mis à jour dans le store:', servicesResult.data?.length || 0, 'services');
        }
      }
    } catch (error) {
      console.log('❌ Erreur de connexion:', error.message);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(selectedCategory === category.id ? null : category.id);
    setFilters({ ...filters, category: selectedCategory === category.id ? null : category.id });
  };

  const handleSortChange = (sortType) => {
    setSortBy(sortType);
    // Ici vous appliqueriez le tri
  };

  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Ionicons name="search" size={20} color={COLORS.gray} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={
            userRole === 'client' 
              ? "Rechercher un service..." 
              : "Rechercher une demande..."
          }
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={COLORS.gray}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={COLORS.gray} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderCategoryFilters = () => (
    <View style={styles.categoriesContainer}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesScroll}
      >
        {SERVICE_CATEGORIES.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryFilter,
              selectedCategory === category.id && styles.categoryFilterActive
            ]}
            onPress={() => handleCategoryFilter(category)}
          >
            <Ionicons 
              name={category.icon} 
              size={16} 
              color={selectedCategory === category.id ? COLORS.white : category.color} 
            />
            <Text style={[
              styles.categoryFilterText,
              selectedCategory === category.id && styles.categoryFilterTextActive
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderTabBar = () => {
    if (userRole === 'client') {
      return (
        <View>
          <View style={styles.tabBar}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 0 && styles.activeTab]}
              onPress={() => setActiveTab(0)}
            >
              <Text style={[styles.tabText, activeTab === 0 && styles.activeTabText]}>
                Services disponibles
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 1 && styles.activeTab]}
              onPress={() => setActiveTab(1)}
            >
              <Text style={[styles.tabText, activeTab === 1 && styles.activeTabText]}>
                Mes demandes
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Sélecteur de statut pour les demandes du client */}
          {activeTab === 1 && (
            <View style={styles.statusFilterContainer}>
              <Text style={styles.filterLabel}>Filtrer par statut:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statusFilterScroll}>
                <TouchableOpacity
                  style={[styles.statusFilterButton, !selectedStatus && styles.statusFilterButtonActive]}
                  onPress={() => setSelectedStatus(null)}
                >
                  <Text style={[styles.statusFilterText, !selectedStatus && styles.statusFilterTextActive]}>
                    Tous
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.statusFilterButton, selectedStatus === 'pending' && styles.statusFilterButtonActive]}
                  onPress={() => setSelectedStatus('pending')}
                >
                  <Text style={[styles.statusFilterText, selectedStatus === 'pending' && styles.statusFilterTextActive]}>
                    En attente
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.statusFilterButton, selectedStatus === 'in_progress' && styles.statusFilterButtonActive]}
                  onPress={() => setSelectedStatus('in_progress')}
                >
                  <Text style={[styles.statusFilterText, selectedStatus === 'in_progress' && styles.statusFilterTextActive]}>
                    En cours
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.statusFilterButton, selectedStatus === 'completed' && styles.statusFilterButtonActive]}
                  onPress={() => setSelectedStatus('completed')}
                >
                  <Text style={[styles.statusFilterText, selectedStatus === 'completed' && styles.statusFilterTextActive]}>
                    Terminées
                  </Text>
                </TouchableOpacity>
              </ScrollView>
            </View>
          )}
        </View>
      );
    } else {
      return (
        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 0 && styles.activeTab]}
            onPress={() => setActiveTab(0)}
          >
            <Text style={[styles.tabText, activeTab === 0 && styles.activeTabText]}>
              Demandes reçues
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 1 && styles.activeTab]}
            onPress={() => setActiveTab(1)}
          >
            <Text style={[styles.tabText, activeTab === 1 && styles.activeTabText]}>
              Mes services
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
  };

  const renderServiceCard = (service) => {
    // Debug: afficher les informations du prestataire
    console.log('🔍 Rendu service:', service.title);
    console.log('🔍 providerId:', service.providerId);
    console.log('🔍 provider:', service.provider);
    console.log('🔍 providerName:', service.providerName);
    
    return (
      <Card key={service._id || service.id} style={styles.serviceCard}>
        <View style={styles.serviceHeader}>
          <View style={styles.serviceIcon}>
            <Ionicons name="construct" size={24} color={COLORS.primary} />
          </View>
          <View style={styles.serviceInfo}>
            <Text style={styles.serviceTitle}>{service.title || 'Sans titre'}</Text>
            <Text style={styles.serviceProvider}>
              {service.providerId?.name || service.provider?.name || service.providerName || 
               (service.providerId?.email ? service.providerId.email.split('@')[0] : 'Prestataire')}
            </Text>
            <View style={styles.providerDetails}>
              <Text style={styles.servicePrice}>{service.price || 0}€/h</Text>
              {service.providerId?.phone && (
                <Text style={styles.providerPhone}>
                  📞 {service.providerId.phone}
                </Text>
              )}
            </View>
          </View>
          <View style={styles.serviceRating}>
            <Ionicons name="star" size={16} color={COLORS.warning} />
            <Text style={styles.ratingText}>
              {service.providerId?.averageRating || service.rating || 0}
            </Text>
            {service.providerId?.totalRatings > 0 && (
              <Text style={styles.ratingCount}>({service.providerId.totalRatings})</Text>
            )}
          </View>
        </View>
        <Text style={styles.serviceDescription}>{service.description || 'Aucune description'}</Text>
        <View style={styles.serviceActions}>
          <Button 
            title="Voir détails" 
            variant="outline" 
            onPress={() => navigation.navigate('ServiceDetail', { service })}
          />
          {userRole === 'client' && (
            <Button 
              title="Demander" 
              onPress={() => navigation.navigate('CreateServiceRequest', { serviceId: service._id || service.id })}
            />
          )}
        </View>
      </Card>
    );
  };

  const renderRequestCard = (request) => (
    <Card key={request._id || request.id} style={styles.serviceCard}>
      <View style={styles.serviceHeader}>
        <View style={styles.serviceIcon}>
          <Ionicons name="document-text" size={24} color={COLORS.secondary} />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceTitle}>{request.title || 'Sans titre'}</Text>
          <Text style={styles.serviceProvider}>
            {userRole === 'client' ? 'Votre demande' : `Client: ${request.clientId || 'Inconnu'}`}
          </Text>
          <Text style={styles.servicePrice}>Budget: {request.budget || 0}€</Text>
        </View>
        <View style={styles.serviceStatus}>
          <Text style={[styles.statusText, { color: getStatusColor(request.status) }]}>
            {request.status || 'Inconnu'}
          </Text>
        </View>
      </View>
      <Text style={styles.serviceDescription}>{request.description || 'Aucune description'}</Text>
      <View style={styles.serviceActions}>
        <Button 
          title="Voir détails" 
          variant="outline" 
          onPress={() => navigation.navigate('ServiceDetail', { request })}
        />
        {userRole === 'provider' && request.status === 'pending' && (
          <Button 
            title="Accepter" 
            onPress={() => handleAcceptRequest(request._id || request.id)}
          />
        )}
      </View>
    </Card>
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return COLORS.warning;
      case 'in_progress': return COLORS.primary;
      case 'completed': return COLORS.success;
      case 'cancelled': return COLORS.danger;
      default: return COLORS.gray;
    }
  };

  const handleAcceptRequest = (requestId) => {
    // Logique pour accepter une demande
    console.log('Demande acceptée:', requestId);
  };

  const renderContent = () => {
    if (userRole === 'client') {
      if (activeTab === 0) {
        // Services disponibles des prestataires
        return (
          <FlatList
            data={services}
            renderItem={({ item }) => renderServiceCard(item)}
            keyExtractor={(item) => (item._id || item.id || Math.random()).toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        );
      } else {
        // Mes demandes
        return (
          <FlatList
            data={serviceRequests.filter(req => req.clientId === user?.id)}
            renderItem={({ item }) => renderRequestCard(item)}
            keyExtractor={(item) => (item._id || item.id || Math.random()).toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        );
      }
    } else {
      if (activeTab === 0) {
        // Demandes reçues des clients
        return (
          <FlatList
            data={serviceRequests}
            renderItem={({ item }) => renderRequestCard(item)}
            keyExtractor={(item) => (item._id || item.id || Math.random()).toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        );
      } else {
        // Mes services
        return (
          <FlatList
            data={myServices}
            renderItem={({ item }) => renderServiceCard(item)}
            keyExtractor={(item) => (item._id || item.id || Math.random()).toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        );
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {userRole === 'client' ? 'Services & Demandes' : 'Demandes & Services'}
        </Text>
        {userRole === 'provider' && (
          <TouchableOpacity
            style={styles.createServiceButton}
            onPress={() => navigation.navigate('CreateProviderService')}
          >
            <Ionicons name="add" size={20} color={COLORS.white} />
            <Text style={styles.createServiceButtonText}>Créer</Text>
          </TouchableOpacity>
        )}
      </View>

      {renderSearchBar()}
      {renderCategoryFilters()}
      {renderTabBar()}

             <View style={styles.content}>
         <RefreshControl
           refreshing={refreshing}
           onRefresh={onRefresh}
           colors={[COLORS.primary]}
           tintColor={COLORS.primary}
         />
         {renderContent()}
       </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SIZES.padding,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
  },
  createServiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.medium,
    paddingVertical: SIZES.base,
    borderRadius: SIZES.radius,
  },
  createServiceButtonText: {
    marginLeft: SIZES.base,
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.white,
  },
  searchContainer: {
    padding: SIZES.padding,
    backgroundColor: COLORS.white,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light,
    borderRadius: SIZES.radius,
    paddingHorizontal: SIZES.medium,
  },
  searchIcon: {
    marginRight: SIZES.base,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SIZES.medium,
    fontSize: SIZES.font,
    color: COLORS.dark,
  },
  categoriesContainer: {
    paddingVertical: SIZES.base,
    backgroundColor: COLORS.white,
  },
  categoriesScroll: {
    paddingHorizontal: SIZES.padding,
  },
  categoryFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light,
    paddingHorizontal: SIZES.medium,
    paddingVertical: SIZES.base,
    borderRadius: SIZES.radius,
    marginRight: SIZES.base,
  },
  categoryFilterActive: {
    backgroundColor: COLORS.primary,
  },
  categoryFilterText: {
    marginLeft: SIZES.base,
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  categoryFilterTextActive: {
    color: COLORS.white,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    paddingHorizontal: SIZES.padding,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tab: {
    flex: 1,
    paddingVertical: SIZES.medium,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: COLORS.primary,
  },
  tabText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.gray,
  },
  activeTabText: {
    color: COLORS.primary,
    fontFamily: FONTS.bold,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    padding: SIZES.padding,
  },
  serviceCard: {
    marginBottom: SIZES.medium,
    padding: SIZES.medium,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.medium,
  },
  serviceIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SIZES.medium,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: SIZES.base,
  },
  serviceProvider: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
    marginBottom: SIZES.base,
  },
  servicePrice: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.success,
  },
  providerDetails: {
    flexDirection: 'column',
    gap: SIZES.base,
  },
  providerPhone: {
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
  serviceRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: SIZES.base,
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  ratingCount: {
    marginLeft: SIZES.base,
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
  serviceStatus: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    fontWeight: 'bold',
  },
  serviceDescription: {
    fontSize: SIZES.font,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
    marginBottom: SIZES.medium,
    lineHeight: 20,
  },
  serviceActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  // Styles pour le sélecteur de statut
  statusFilterContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: SIZES.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  filterLabel: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: SIZES.base,
    paddingHorizontal: SIZES.padding,
  },
  statusFilterScroll: {
    paddingHorizontal: SIZES.padding,
  },
  statusFilterButton: {
    paddingHorizontal: SIZES.medium,
    paddingVertical: SIZES.base,
    marginRight: SIZES.base,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.light,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  statusFilterButtonActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  statusFilterText: {
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  statusFilterTextActive: {
    color: COLORS.white,
  },
});

export default ServicesListScreen;
