export async function sendSms(to, text) {
  if (!process.env.TWILIO_SID || !process.env.TWILIO_TOKEN || !process.env.TWILIO_FROM) {
    console.warn('<PERSON><PERSON><PERSON> not configured. Pretending to send SMS to', to);
    return;
  }
  const twilio = (await import('twilio')).default;
  const client = twilio(process.env.TWILIO_SID, process.env.TWILIO_TOKEN);
  await client.messages.create({ from: process.env.TWILIO_FROM, to, body: text });
} 