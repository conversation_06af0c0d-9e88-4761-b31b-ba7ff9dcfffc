# WeServe Frontend Development Guide

## Overview
This guide covers frontend development for the WeServe platform - a service marketplace connecting clients with local service providers. The frontend should be built as a responsive web application that works seamlessly on both desktop and mobile devices.

## Tech Stack Recommendations

### Core Framework
- **React 18+** with **JavaScript** (ES6+)
- **Next.js 14+** for SSR/SSG capabilities and better SEO
- **Vite** as an alternative for SPA-only builds

### State Management
- **Zustand** (lightweight) or **Redux Toolkit** (enterprise)
- **React Query/TanStack Query** for server state management
- **React Context** for simple global state

### UI Framework
- **Tailwind CSS** for utility-first styling
- **Shadcn/ui** or **Radix UI** for accessible components
- **Framer Motion** for animations
- **React Hook Form** + **Zod** for form handling

### Authentication & Security
- **HttpOnly cookies** (handled by backend)
- **Axios** or **Fetch API** with interceptors
- **React Router** for client-side routing

## Project Structure

```
weserve-frontend/
├── src/
│   ├── components/
│   │   ├── ui/                 # Reusable UI components
│   │   ├── forms/              # Form components
│   │   ├── layout/             # Layout components
│   │   └── features/           # Feature-specific components
│   ├── pages/                  # Page components
│   ├── hooks/                  # Custom React hooks
│   ├── services/               # API service functions
│   ├── stores/                 # State management
│   ├── utils/                  # Utility functions
│   └── styles/                 # Global styles
├── public/                     # Static assets
├── package.json
└── README.md
```

## Core Features Implementation

### 1. Authentication System

#### Login/Signup Forms
```typescript
// src/components/forms/AuthForm.tsx
interface AuthFormProps {
  mode: 'login' | 'signup';
  onSubmit: (data: AuthFormData) => void;
}

// Form fields:
// - Email/Phone
// - Password
// - Name (signup only)
// - Role selection (signup only)
// - Social login buttons (Google, Facebook)
```

#### Social Authentication
```javascript
// src/services/authService.js
export const initiateGoogleAuth = () => {
  window.location.href = `${API_BASE}/auth/google`;
};

export const initiateFacebookAuth = () => {
  window.location.href = `${API_BASE}/auth/facebook`;
};
```

#### Protected Routes
```javascript
// src/components/auth/ProtectedRoute.jsx
// Props:
// - children: React component to render
// - allowedRoles: array of allowed user roles
// - redirectTo: path to redirect unauthorized users
```

### 2. Service Request Management

#### Create Service Request
```javascript
// src/components/forms/ServiceRequestForm.jsx
// Form data structure:
// {
//   title: string,
//   description: string,
//   photos: File[],
//   location: {
//     lat: number,
//     lng: number
//   },
//   date: Date,
//   budget: number
// }
```

#### Photo Upload
```javascript
// src/components/forms/PhotoUpload.jsx
// Props:
// - maxPhotos: maximum number of photos allowed
// - onPhotosChange: callback when photos change
// - existingPhotos: array of existing photo URLs
```

#### Location Picker
```javascript
// src/components/forms/LocationPicker.jsx
// Props:
// - onLocationChange: callback when location changes
// - initialLocation: initial coordinates { lat, lng }
```

### 3. Service Discovery

#### Service List
```javascript
// src/components/features/ServiceList.jsx
// Props:
// - filters: object with category, location, budgetRange, date
// - onServiceSelect: callback when service is selected

// Filters structure:
// {
//   category: string,
//   location: string,
//   budgetRange: [min, max],
//   date: Date
// }
```

#### Service Card
```javascript
// src/components/features/ServiceCard.jsx
// Props:
// - service: service request object
// - onClick: callback when card is clicked
// - showActions: boolean to show action buttons
```

### 4. Dashboard & Profile

#### Client Dashboard
```javascript
// src/pages/dashboard/ClientDashboard.jsx
// Features:
// - Active service requests
// - Request history
// - Provider ratings
// - Payment history
```

#### Provider Dashboard
```javascript
// src/pages/dashboard/ProviderDashboard.jsx
// Features:
// - Available service requests
// - Accepted requests
// - Completed services
// - Earnings overview
```

#### User Profile
```javascript
// src/components/features/UserProfile.jsx
// Props:
// - user: user object with profile data
// - isEditable: boolean to enable editing
// - onUpdate: callback when profile is updated
```

## API Integration

### HTTP Client Setup
```javascript
// src/services/api.js
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:4000/api/v1',
  withCredentials: true, // Important for HttpOnly cookies
});

// Request interceptor
api.interceptors.request.use((config) => {
  // Add any request headers if needed
  return config;
});

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh or redirect to login
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### Service Functions
```javascript
// src/services/serviceRequestService.js
export const createServiceRequest = async (data) => {
  const formData = new FormData();
  formData.append('title', data.title);
  formData.append('description', data.description);
  formData.append('lat', data.location.lat.toString());
  formData.append('lng', data.location.lng.toString());
  formData.append('date', data.date.toISOString());
  formData.append('budget', data.budget.toString());
  
  data.photos.forEach((photo) => {
    formData.append('photos', photo);
  });

  const response = await api.post('/services', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
};
```

## State Management

### Authentication Store
```javascript
// src/stores/authStore.js
// State structure:
// {
//   user: User object or null,
//   isAuthenticated: boolean,
//   isLoading: boolean
// }

// Actions:
// - login: function to handle user login
// - logout: function to handle user logout
// - refreshUser: function to refresh user data
```

### Service Request Store
```javascript
// src/stores/serviceRequestStore.js
// State structure:
// {
//   requests: array of service requests,
//   currentRequest: current service request or null,
//   filters: filter object,
//   isLoading: boolean
// }

// Actions:
// - fetchRequests: function to fetch service requests
// - createRequest: function to create new request
// - updateRequestStatus: function to update request status
```

## UI/UX Guidelines

### Design System
- **Color Palette**: Primary, secondary, accent colors
- **Typography**: Consistent font hierarchy
- **Spacing**: 8px grid system
- **Components**: Reusable, accessible components

### Responsive Design
- **Mobile-first** approach
- **Breakpoints**: 320px, 768px, 1024px, 1440px
- **Touch-friendly** interactions
- **Progressive enhancement**

### Accessibility
- **WCAG 2.1 AA** compliance
- **Keyboard navigation** support
- **Screen reader** compatibility
- **Color contrast** ratios

## Testing Strategy

### Unit Tests
- **Jest** + **React Testing Library**
- Component rendering tests
- Hook testing
- Utility function testing

### Integration Tests
- **Cypress** or **Playwright**
- User flow testing
- API integration testing
- Cross-browser compatibility

### E2E Tests
- Critical user journeys
- Payment flows
- Authentication flows

## Performance Optimization

### Code Splitting
```javascript
// src/App.jsx
import { lazy, Suspense } from 'react';

const Dashboard = lazy(() => import('./pages/Dashboard'));
const ServiceList = lazy(() => import('./pages/ServiceList'));

// Wrap with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <Dashboard />
</Suspense>
```

### Image Optimization
- **WebP** format support
- **Responsive images** with srcset
- **Lazy loading** for images
- **CDN** integration

### Bundle Optimization
- **Tree shaking** for unused code
- **Code splitting** by routes
- **Dynamic imports** for heavy components

## Deployment

### Build Process
```bash
# Development
npm run dev

# Production build
npm run build

# Preview build
npm run preview
```

### Environment Configuration
```bash
# .env.local
REACT_APP_API_BASE_URL=http://localhost:4000/api/v1
REACT_APP_GOOGLE_MAPS_API_KEY=your_key_here
REACT_APP_CLOUDINARY_CLOUD_NAME=your_cloud_name
```

### Deployment Platforms
- **Vercel** (recommended for Next.js)
- **Netlify** (SPA deployment)
- **AWS S3 + CloudFront**
- **Firebase Hosting**

## Security Considerations

### Frontend Security
- **Input validation** on client-side
- **XSS prevention** with proper escaping
- **CSRF protection** (handled by backend)
- **Content Security Policy** headers

### Data Protection
- **Sensitive data** not stored in localStorage
- **Secure communication** over HTTPS
- **Token management** via HttpOnly cookies

## Monitoring & Analytics

### Error Tracking
- **Sentry** for error monitoring
- **LogRocket** for session replay
- **Custom error boundaries**

### Performance Monitoring
- **Web Vitals** tracking
- **Core Web Vitals** optimization
- **Bundle analyzer** integration

### User Analytics
- **Google Analytics 4**
- **Mixpanel** for user behavior
- **Custom event tracking**

## Development Workflow

### Git Workflow
```bash
# Feature branch workflow
git checkout -b feature/service-request-form
git add .
git commit -m "feat: add service request form component"
git push origin feature/service-request-form
# Create PR for review
```

### Code Quality
- **ESLint** configuration
- **Prettier** formatting
- **Husky** pre-commit hooks
- **JavaScript** best practices

### Code Review Checklist
- [ ] Component reusability
- [ ] Performance considerations
- [ ] Accessibility compliance
- [ ] Test coverage
- [ ] JavaScript best practices
- [ ] Error handling

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Git

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd weserve-frontend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

### Development Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
```

## Troubleshooting

### Common Issues
1. **CORS errors**: Ensure backend CORS is configured correctly
2. **Cookie issues**: Check HttpOnly cookie settings
3. **Build failures**: Verify Node.js version compatibility
4. **JavaScript errors**: Check browser console for detailed errors

### Performance Issues
1. **Bundle size**: Use bundle analyzer to identify large packages
2. **Rendering**: Implement React.memo and useMemo where appropriate
3. **Images**: Optimize and lazy load images
4. **API calls**: Implement proper caching and request deduplication

## Resources

### Documentation
- [React Documentation](https://react.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [JavaScript MDN Web Docs](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

### Tools
- [React Developer Tools](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)
- [Redux DevTools](https://chrome.google.com/webstore/detail/redux-devtools/lmhkpmbekcpmknklioeibfkpmmfibljd)
- [Postman](https://www.postman.com/) for API testing

### Community
- [React Community](https://reactjs.org/community/support.html)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/reactjs)
- [GitHub Discussions](https://github.com/facebook/react/discussions)

---

This guide provides a comprehensive foundation for frontend development on the WeServe platform. Adapt and extend based on your specific requirements and team preferences. 