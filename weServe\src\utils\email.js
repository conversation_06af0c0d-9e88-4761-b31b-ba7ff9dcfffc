import nodemailer from 'nodemailer';

export async function sendEmail(to, subject, text) {
  if (!process.env.SMTP_HOST) {
    console.warn('SMTP not configured. Pretending to send email to', to);
    return;
  }
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    secure: false,
    auth: { user: process.env.SMTP_USER, pass: process.env.SMTP_PASS }
  });
  await transporter.sendMail({ from: process.env.SMTP_FROM || '<EMAIL>', to, subject, text });
} 