{"expo": {"name": "WeServe", "slug": "weserve-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#2563EB"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.weserve.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#2563EB"}, "package": "com.weserve.app", "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.POST_NOTIFICATIONS"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-camera", "expo-image-picker", "expo-notifications"], "sdkVersion": "52.0.0", "newArchEnabled": true}}