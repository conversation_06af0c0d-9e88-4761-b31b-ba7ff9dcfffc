# ========================================
#    Tests JMeter pour weServeApp
# ========================================

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Tests JMeter pour weServeApp" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour trouver JMeter
function Find-JMeter {
    Write-Host "🔍 Recherche de JMeter..." -ForegroundColor Yellow
    
    # Essayer de trouver JMeter dans le PATH
    try {
        $jmeterPath = Get-Command jmeter -ErrorAction Stop
        Write-Host "✅ JMeter détecté dans le PATH!" -ForegroundColor Green
        Write-Host "Chemin: $($jmeterPath.Source)" -ForegroundColor Gray
        return $jmeterPath.Source
    } catch {
        Write-Host "⚠️ JMeter non trouvé dans le PATH, recherche dans les emplacements courants..." -ForegroundColor Yellow
    }
    
    # Emplacements courants à vérifier
    $commonPaths = @(
        "C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat",
        "C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat",
        "$env:USERPROFILE\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat",
        "$env:USERPROFILE\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat",
        "C:\apache-jmeter-5.6.3\bin\jmeter.bat"
    )
    
    # Vérifier chaque emplacement
    foreach ($path in $commonPaths) {
        if (Test-Path $path) {
            Write-Host "✅ JMeter trouvé: $path" -ForegroundColor Green
            return $path
        }
    }
    
    # Si JMeter n'est trouvé nulle part
    Write-Host "❌ JMeter n'a pas été trouvé automatiquement" -ForegroundColor Red
    Write-Host ""
    Write-Host "Veuillez:" -ForegroundColor Yellow
    Write-Host "1. Installer JMeter depuis: https://jmeter.apache.org/download_jmeter.cgi" -ForegroundColor White
    Write-Host "2. Ou ajouter JMeter au PATH système" -ForegroundColor White
    Write-Host "3. Ou spécifier manuellement le chemin" -ForegroundColor White
    Write-Host ""
    
    $customPath = Read-Host "Chemin complet vers jmeter.bat (ou appuyez sur Entrée pour quitter)"
    if ($customPath -and (Test-Path $customPath)) {
        Write-Host "✅ Chemin personnalisé accepté: $customPath" -ForegroundColor Green
        return $customPath
    } else {
        Write-Host "❌ Chemin invalide ou annulé" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour quitter"
        exit 1
    }
}

# Trouver JMeter
$jmeterCmd = Find-JMeter
Write-Host ""
Write-Host "🎯 JMeter sera exécuté avec: $jmeterCmd" -ForegroundColor Cyan
Write-Host ""

# Créer le dossier de résultats s'il n'existe pas
if (!(Test-Path "jmeter_results")) {
    New-Item -ItemType Directory -Name "jmeter_results" | Out-Null
    Write-Host "📁 Dossier jmeter_results créé" -ForegroundColor Green
}

function Show-Menu {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    Menu principal" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Test de fumée (1 utilisateur, test rapide)" -ForegroundColor White
    Write-Host "2. Test de charge (10 utilisateurs, test complet)" -ForegroundColor White
    Write-Host "3. Test de stress (50 utilisateurs, test intensif)" -ForegroundColor White
    Write-Host "4. Test personnalisé" -ForegroundColor White
    Write-Host "5. Quitter" -ForegroundColor White
    Write-Host ""
}

function Run-SmokeTest {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "    Exécution du test de fumée..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Configuration:" -ForegroundColor White
    Write-Host "- Utilisateurs: 1" -ForegroundColor Gray
    Write-Host "- Durée: ~30 secondes" -ForegroundColor Gray
    Write-Host "- Objectif: Validation de la configuration" -ForegroundColor Gray
    Write-Host ""
    
    $confirm = Read-Host "Confirmer l'exécution du test de fumée? (o/n)"
    if ($confirm -eq "o" -or $confirm -eq "O") {
        Write-Host "🚀 Démarrage du test de fumée..." -ForegroundColor Green
        try {
            & $jmeterCmd -n -t weServeApp_TestPlan.jmx -l jmeter_results/smoke_test_results.jtl -e -o jmeter_results/smoke_test_report
            Write-Host ""
            Write-Host "✅ Test de fumée terminé!" -ForegroundColor Green
            Write-Host "📊 Résultats disponibles dans: jmeter_results/smoke_test_results.jtl" -ForegroundColor Cyan
            Write-Host "📝 Logs disponibles dans: jmeter_results/smoke_test_results.jtl" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ Erreur lors de l'exécution du test: $_" -ForegroundColor Red
        }
        Write-Host ""
        Read-Host "Appuyez sur Entrée pour continuer"
    }
}

function Run-LoadTest {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "    Exécution du test de charge..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Configuration:" -ForegroundColor White
    Write-Host "- Utilisateurs: 10" -ForegroundColor Gray
    Write-Host "- Durée: ~2 minutes" -ForegroundColor Gray
    Write-Host "- Objectif: Test de performance normale" -ForegroundColor Gray
    Write-Host ""
    
    $confirm = Read-Host "Confirmer l'exécution du test de charge? (o/n)"
    if ($confirm -eq "o" -or $confirm -eq "O") {
        Write-Host "🚀 Démarrage du test de charge..." -ForegroundColor Green
        try {
            & $jmeterCmd -n -t weServeApp_TestPlan.jmx -l jmeter_results/load_test_results.jtl -e -o jmeter_results/load_test_report
            Write-Host ""
            Write-Host "✅ Test de charge terminé!" -ForegroundColor Green
            Write-Host "📊 Résultats disponibles dans: jmeter_results/load_test_results.jtl" -ForegroundColor Cyan
            Write-Host "📝 Logs disponibles dans: jmeter_results/load_test_results.jtl" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ Erreur lors de l'exécution du test: $_" -ForegroundColor Red
        }
        Write-Host ""
        Read-Host "Appuyez sur Entrée pour continuer"
    }
}

function Run-StressTest {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "    Exécution du test de stress..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Configuration:" -ForegroundColor White
    Write-Host "- Utilisateurs: 50" -ForegroundColor Gray
    Write-Host "- Durée: ~5 minutes" -ForegroundColor Gray
    Write-Host "- Objectif: Test des limites de performance" -ForegroundColor Gray
    Write-Host ""
    
    $confirm = Read-Host "Confirmer l'exécution du test de stress? (o/n)"
    if ($confirm -eq "o" -or $confirm -eq "O") {
        Write-Host "🚀 Démarrage du test de stress..." -ForegroundColor Green
        try {
            & $jmeterCmd -n -t weServeApp_TestPlan.jmx -l jmeter_results/stress_test_results.jtl -e -o jmeter_results/stress_test_report
            Write-Host ""
            Write-Host "✅ Test de stress terminé!" -ForegroundColor Green
            Write-Host "📊 Résultats disponibles dans: jmeter_results/stress_test_results.jtl" -ForegroundColor Cyan
            Write-Host "📝 Logs disponibles dans: jmeter_results/stress_test_results.jtl" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ Erreur lors de l'exécution du test: $_" -ForegroundColor Red
        }
        Write-Host ""
        Read-Host "Appuyez sur Entrée pour continuer"
    }
}

function Run-CustomTest {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "    Test personnalisé" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host ""
    
    $users = Read-Host "Nombre d'utilisateurs"
    $duration = Read-Host "Durée en secondes"
    $rampup = Read-Host "Temps de montée en charge (secondes)"
    
    Write-Host ""
    Write-Host "Configuration personnalisée:" -ForegroundColor White
    Write-Host "- Utilisateurs: $users" -ForegroundColor Gray
    Write-Host "- Durée: $duration secondes" -ForegroundColor Gray
    Write-Host "- Ramp-up: $rampup secondes" -ForegroundColor Gray
    Write-Host ""
    
    $confirm = Read-Host "Confirmer l'exécution du test personnalisé? (o/n)"
    if ($confirm -eq "o" -or $confirm -eq "O") {
        Write-Host "🚀 Démarrage du test personnalisé..." -ForegroundColor Green
        try {
            & $jmeterCmd -n -t weServeApp_TestPlan.jmx -l jmeter_results/custom_test_results.jtl -e -o jmeter_results/custom_test_report
            Write-Host ""
            Write-Host "✅ Test personnalisé terminé!" -ForegroundColor Green
            Write-Host "📊 Résultats disponibles dans: jmeter_results/custom_test_results.jtl" -ForegroundColor Cyan
            Write-Host "📝 Logs disponibles dans: jmeter_results/custom_test_results.jtl" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ Erreur lors de l'exécution du test: $_" -ForegroundColor Red
        }
        Write-Host ""
        Read-Host "Appuyez sur Entrée pour continuer"
    }
}

# Boucle principale du menu
do {
    Show-Menu
    $choice = Read-Host "Votre choix (1-5)"
    
    switch ($choice) {
        "1" { Run-SmokeTest }
        "2" { Run-LoadTest }
        "3" { Run-StressTest }
        "4" { Run-CustomTest }
        "5" { 
            Write-Host ""
            Write-Host "Merci d'avoir utilisé les tests JMeter pour weServeApp!" -ForegroundColor Green
            Write-Host ""
            break
        }
        default { 
            Write-Host ""
            Write-Host "❌ Choix invalide! Veuillez choisir entre 1 et 5." -ForegroundColor Red
            Write-Host ""
            Read-Host "Appuyez sur Entrée pour continuer"
        }
    }
} while ($choice -ne "5")

Read-Host "Appuyez sur Entrée pour quitter" 