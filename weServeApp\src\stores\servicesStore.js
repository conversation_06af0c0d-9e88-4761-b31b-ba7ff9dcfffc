import { create } from 'zustand';
import servicesService from '../services/servicesService';

export const useServicesStore = create((set, get) => ({
  services: [], // Vrais services des prestataires depuis l'API
  serviceRequests: [], // Vraies demandes depuis l'API
  myServices: [],
  isLoading: false,
  error: null,
  filters: {
    category: null,
    priceRange: null,
    location: null,
    rating: null
  },
  
  setServices: (services) => set({ services }),
  
  setServiceRequests: (requests) => set({ serviceRequests: requests }),
  
  setMyServices: (services) => set({ myServices: services }),
  
  // Charger les demandes de service depuis l'API
  loadServiceRequests: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await servicesService.getOpenServiceRequests();
      if (response.success) {
        set({ serviceRequests: response.data || [] });
      } else {
        set({ error: response.message || 'Erreur lors du chargement des demandes' });
      }
    } catch (error) {
      set({ error: error.message || 'Erreur lors du chargement des demandes' });
    } finally {
      set({ isLoading: false });
    }
  },
  
  // Charger mes demandes de service (pour clients)
  loadMyServiceRequests: async (filters = {}) => {
    set({ isLoading: true, error: null });
    try {
      const response = await servicesService.getMyServiceRequests(filters);
      if (response.success) {
        set({ serviceRequests: response.data || [] });
      } else {
        set({ error: response.message || 'Erreur lors du chargement de mes demandes' });
      }
    } catch (error) {
      set({ error: error.message || 'Erreur lors du chargement de mes demandes' });
    } finally {
      set({ isLoading: false });
    }
  },
  
  addService: (service) => set(state => ({
    services: [...state.services, service]
  })),
  
  addServiceRequest: (request) => set(state => ({
    serviceRequests: [...state.serviceRequests, request]
  })),
  
  updateService: (id, updates) => set(state => ({
    services: state.services.map(service => 
      service.id === id ? { ...service, ...updates } : service
    )
  })),
  
  updateServiceRequest: (id, updates) => set(state => ({
    serviceRequests: state.serviceRequests.map(request => 
      request.id === id ? { ...request, ...updates } : request
    )
  })),
  
  removeService: (id) => set(state => ({
    services: state.services.filter(service => service.id !== id)
  })),
  
  removeServiceRequest: (id) => set(state => ({
    serviceRequests: state.serviceRequests.filter(request => request.id !== id)
  })),
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  setError: (error) => set({ error, isLoading: false }),
  
  clearError: () => set({ error: null }),
  
  setFilters: (filters) => set({ filters: { ...get().filters, ...filters } }),
  
  clearFilters: () => set({ 
    filters: {
      category: null,
      priceRange: null,
      location: null,
      rating: null
    }
  }),
  
  // Fonction pour filtrer les services
  getFilteredServices: () => {
    const { services, filters } = get();
    let filtered = [...services];
    
    if (filters.category) {
      filtered = filtered.filter(service => service.category === filters.category);
    }
    
    if (filters.priceRange) {
      filtered = filtered.filter(service => service.price <= filters.priceRange);
    }
    
    if (filters.rating) {
      filtered = filtered.filter(service => service.rating >= filters.rating);
    }
    
    return filtered;
  },
  
  // Fonction pour filtrer les demandes
  getFilteredRequests: () => {
    const { serviceRequests, filters } = get();
    let filtered = [...serviceRequests];
    
    if (filters.category) {
      filtered = filtered.filter(request => request.category === filters.category);
    }
    
    if (filters.priceRange) {
      filtered = filtered.filter(request => request.budget <= filters.priceRange);
    }
    
    return filtered;
  }
}));
