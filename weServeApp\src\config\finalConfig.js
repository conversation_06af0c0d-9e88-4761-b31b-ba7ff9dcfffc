import { Platform } from 'react-native';
import { getSafeGestureConfig } from '../utils/gestureHandlerConfig';

// Configuration finale pour résoudre l'erreur topInsetsChange sur Expo SDK 52
export const finalConfig = {
  // Configuration de base
  base: {
    enableScreens: false, // Désactiver complètement les écrans natifs
    enableAnimations: false, // Désactiver toutes les animations
    enableGestures: false, // Désactiver les gestes problématiques
  },
  
  // Configuration de navigation
  navigation: {
    // Configuration des écrans de pile
    stack: {
      screenOptions: {
        headerShown: false,
        gestureEnabled: false,
        animationEnabled: false,
        // Transition simple sans animation
        cardStyleInterpolator: ({ current, layouts }) => ({
          cardStyle: {
            transform: [
              {
                translateX: current.progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [layouts.screen.width, 0],
                }),
              },
            ],
          },
        }),
      },
    },
    
    // Configuration des onglets
    tabs: {
      screenOptions: {
        headerShown: false,
        // Style des onglets
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#E5E7EB',
          height: Platform.OS === 'ios' ? 88 : 60,
          paddingBottom: Platform.OS === 'ios' ? 20 : 8,
          paddingTop: 8,
        },
        tabBarActiveTintColor: '#2563EB',
        tabBarInactiveTintColor: '#6B7280',
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      },
    },
  },
  
  // Configuration des safe areas
  safeArea: {
    // Désactiver les safe areas problématiques
    enableSafeArea: false,
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 },
  },
  
  // Configuration des transitions
  transitions: {
    // Transitions simples sans animation
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
  },
  
  // Configuration spécifique par plateforme
  platform: {
    android: {
      // Désactiver complètement sur Android
      enableScreens: false,
      enableAnimations: false,
      enableGestures: false,
      enableSafeArea: false,
    },
    ios: {
      // Configuration minimale sur iOS
      enableScreens: false,
      enableAnimations: false,
      enableGestures: false,
      enableSafeArea: false,
    },
  },
};

// Fonction pour obtenir la configuration selon la plateforme
export const getPlatformConfig = () => {
  const platform = Platform.OS;
  return {
    ...finalConfig.base,
    ...finalConfig.navigation,
    ...finalConfig.safeArea,
    ...finalConfig.transitions,
    ...finalConfig.platform[platform],
  };
};

// Configuration pour éviter complètement l'erreur topInsetsChange
export const getTopInsetsSafeConfig = () => {
  return {
    // Désactiver toutes les fonctionnalités problématiques
    enableScreens: false,
    enableAnimations: false,
    enableGestures: false,
    enableSafeArea: false,
    
    // Configuration des écrans
    screenOptions: {
      headerShown: false,
      gestureEnabled: false,
      animationEnabled: false,
      // Transition simple
      cardStyleInterpolator: ({ current, layouts }) => ({
        cardStyle: {
          transform: [
            {
              translateX: current.progress.interpolate({
                inputRange: [0, 1],
                outputRange: [layouts.screen.width, 0],
              }),
            },
          ],
        },
      }),
    },
    
    // Configuration des onglets
    tabBarOptions: {
      activeTintColor: '#2563EB',
      inactiveTintColor: '#6B7280',
      style: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
      },
    },
  };
};

// Configuration complète sécurisée avec gesture handler
export const getCompleteSafeConfig = () => {
  const gestureConfig = getSafeGestureConfig();
  
  return {
    ...finalConfig,
    ...getTopInsetsSafeConfig(),
    ...gestureConfig,
  };
};

export default finalConfig;
