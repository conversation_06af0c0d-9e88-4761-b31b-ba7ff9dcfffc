{"name": "weserveapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~52.0.0", "expo-camera": "~16.0.18", "expo-image-picker": "~16.0.0", "expo-linear-gradient": "^14.1.5", "expo-location": "~18.0.0", "expo-notifications": "~0.29.14", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.3", "react-native-ratings": "^8.1.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-vector-icons": "^10.0.3", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.20.0", "jest": "^29.7.0", "jest-expo": "~52.0.0"}, "private": true}