import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuthStore } from '../../stores/authStore';
import authService from '../../services/authService';
import { runApiTests } from '../../utils/apiTest';
import Button from '../common/Button';
import Input from '../common/Input';
import Card from '../common/Card';

const LoginScreen = () => {
  const navigation = useNavigation();
  const { login, isLoading, error } = useAuthStore();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'L\'email n\'est pas valide';
    }
    
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;
    
    try {
      const { setLoading, setError, clearError } = useAuthStore.getState();
      setLoading(true);
      clearError();
      
      const result = await authService.login({
        email: formData.email,
        password: formData.password
      });
      
      console.log('🔍 Résultat login:', result);
      
      if (result.success) {
        // La connexion est gérée automatiquement par le service
        // La navigation sera gérée par le store
        console.log('✅ Connexion réussie');
      } else {
        const errorMessage = result.message || 'Échec de la connexion';
        console.log('❌ Erreur login:', errorMessage);
        setError(errorMessage);
      }
    } catch (error) {
      useAuthStore.getState().setError('Erreur de connexion. Veuillez réessayer.');
    } finally {
      useAuthStore.getState().setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>WeServe</Text>
            <Text style={styles.subtitle}>Connectez-vous à votre compte</Text>
          </View>

          <Card style={styles.formCard}>
            <Input
              label="Email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
              error={errors.email}
            />

            <Input
              label="Mot de passe"
              placeholder="Votre mot de passe"
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              secureTextEntry
              error={errors.password}
            />

            {error && (
              <Text style={styles.errorText}>{error}</Text>
            )}

            <Button
              title="Se connecter"
              onPress={handleLogin}
              loading={isLoading}
              style={styles.loginButton}
            />

            <View style={styles.socialLogin}>
              <Text style={styles.socialText}>Ou connectez-vous avec :</Text>
              <View style={styles.socialButtons}>
                <Button
                  title="Google"
                  variant="outline"
                  onPress={() => Alert.alert('Info', 'Connexion Google à implémenter')}
                  style={styles.socialButton}
                />
                <Button
                  title="Facebook"
                  variant="outline"
                  onPress={() => Alert.alert('Info', 'Connexion Facebook à implémenter')}
                  style={styles.socialButton}
                />
              </View>
            </View>

            <View style={styles.footer}>
              <Text style={styles.footerText}>
                Vous n'avez pas de compte ?{' '}
                <Text
                  style={styles.linkText}
                  onPress={() => navigation.navigate('Signup')}
                >
                  S'inscrire
                </Text>
              </Text>
              
              {/* Bouton de test temporaire */}
              <Button
                title="🧪 Tester l'API"
                variant="outline"
                onPress={async () => {
                  console.log('🧪 Test de l\'API...');
                  await runApiTests();
                }}
                style={styles.testButton}
              />
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2563EB',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
  },
  formCard: {
    padding: 24,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  loginButton: {
    marginTop: 24,
    marginBottom: 24,
  },
  socialLogin: {
    marginTop: 24,
    alignItems: 'center',
  },
  socialText: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 16,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  footer: {
    marginTop: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 16,
    color: '#6B7280',
  },
  linkText: {
    color: '#2563EB',
    fontWeight: '600',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  testButton: {
    marginTop: 16,
    backgroundColor: '#F59E0B',
    borderColor: '#F59E0B',
  },
});

export default LoginScreen;
