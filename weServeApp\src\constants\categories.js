export const SERVICE_CATEGORIES = [
  {
    id: 'Plomberie',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    icon: 'water',
    color: '#2563EB',
    description: 'Services de plomberie et réparation'
  },
  {
    id: 'Électricité',
    name: 'Électricité',
    icon: 'flash',
    color: '#F59E0B',
    description: 'Installation et réparation électrique'
  },
  {
    id: '<PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON><PERSON>',
    icon: 'home',
    color: '#10B981',
    description: 'Services de nettoyage et ménage'
  },
  {
    id: 'Jardinage',
    name: 'Jardinage',
    icon: 'leaf',
    color: '#22C55E',
    description: 'Entretien des espaces verts'
  },
  {
    id: 'Déménagement',
    name: 'Déménagement',
    icon: 'truck',
    color: '#F97316',
    description: 'Services de déménagement'
  },
  {
    id: 'Peinture',
    name: 'Peinture',
    icon: 'brush',
    color: '#8B5CF6',
    description: 'Peinture intérieure et extérieure'
  },
  {
    id: 'Réparation',
    name: 'Réparation',
    icon: 'construct',
    color: '#EF4444',
    description: 'Services de réparation générale'
  },
  {
    id: 'Installation',
    name: 'Installation',
    icon: 'settings',
    color: '#06B6D4',
    description: 'Installation d\'équipements'
  },
  {
    id: 'Autre',
    name: 'Autre',
    icon: 'ellipsis-horizontal',
    color: '#6B7280',
    description: 'Autres services'
  }
];

export const getCategoryById = (id) => {
  return SERVICE_CATEGORIES.find(category => category.id === id);
};

export const getCategoryColor = (id) => {
  const category = getCategoryById(id);
  return category ? category.color : COLORS.gray;
};
