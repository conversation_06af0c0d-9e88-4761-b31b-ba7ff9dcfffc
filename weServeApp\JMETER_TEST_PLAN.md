# 🧪 Plan de Test JMeter pour weServeApp

## 📋 **Vue d'ensemble**
Ce document détaille comment configurer et exécuter des tests de performance avec JMeter pour votre application weServeApp.

## 🎯 **Objectifs des Tests**
- **Test de Charge** : Vérifier la capacité de l'API à gérer de multiples utilisateurs simultanés
- **Test de Stress** : Identifier les limites de performance de l'application
- **Test de Fonctionnalité** : Valider que tous les endpoints fonctionnent correctement
- **Test de Sécurité** : Vérifier l'authentification et les autorisations

---

## 🚀 **Configuration Initiale JMeter**

### **1. Installation de JMeter**
```bash
# Télécharger JMeter depuis https://jmeter.apache.org/download_jmeter.cgi
# Ou utiliser Homebrew (macOS)
brew install jmeter

# Ou utiliser Chocolatey (Windows)
choco install jmeter
```

### **2. Structure du Plan de Test**
```
weServeApp_TestPlan.jmx
├── Thread Group - Authentification
├── Thread Group - Services (Clients)
├── Thread Group - Services (Prestataires)
├── Thread Group - Demandes de Service
├── Thread Group - Recherche et Filtres
└── Thread Group - Test de Charge
```

---

## 🔐 **1. Test d'Authentification**

### **Configuration du Thread Group**
- **Nom** : `Authentification`
- **Nombre d'utilisateurs** : 10
- **Ramp-up** : 10 secondes
- **Durée** : 60 secondes

### **Endpoints à Tester**

#### **1.1 Inscription (Signup)**
```
POST /auth/signup
URL: http://localhost:4000/api/v1/auth/signup
Headers: Content-Type: application/json

Body (JSON):
{
  "firstName": "Test${__Random(1,1000)}",
  "lastName": "User${__Random(1,1000)}",
  "email": "test${__Random(1,1000)}@example.com",
  "password": "Test123!",
  "phone": "+33${__Random(100000000,999999999)}",
  "role": "client"
}
```

#### **1.2 Connexion (Login)**
```
POST /auth/login
URL: http://localhost:4000/api/v1/auth/login
Headers: Content-Type: application/json

Body (JSON):
{
  "email": "test${__Random(1,1000)}@example.com",
  "password": "Test123!"
}
```

#### **1.3 Extraction du Token**
- **Extracteur Regex** : `"accessToken":"([^"]+)"`
- **Variable** : `token`

---

## 🛠️ **2. Test des Services Prestataires**

### **Configuration du Thread Group**
- **Nom** : `Services Prestataires`
- **Nombre d'utilisateurs** : 5
- **Ramp-up** : 5 secondes
- **Durée** : 120 secondes

### **Endpoints à Tester**

#### **2.1 Création de Service**
```
POST /provider-services
URL: http://localhost:4000/api/v1/provider-services
Headers: 
  Content-Type: application/json
  Authorization: Bearer ${token}

Body (JSON):
{
  "title": "Service Test ${__Random(1,1000)}",
  "description": "Description du service de test",
  "category": "Plomberie",
  "price": ${__Random(50,200)},
  "location": {
    "address": "123 Rue Test, Paris",
    "latitude": 48.8566,
    "longitude": 2.3522
  },
  "availability": [
    {
      "day": "monday",
      "startTime": "08:00",
      "endTime": "18:00",
      "available": true
    }
  ]
}
```

#### **2.2 Liste des Services**
```
GET /provider-services
URL: http://localhost:4000/api/v1/provider-services
Headers: Authorization: Bearer ${token}
```

#### **2.3 Détail d'un Service**
```
GET /provider-services/{id}
URL: http://localhost:4000/api/v1/provider-services/${serviceId}
Headers: Authorization: Bearer ${token}
```

---

## 📝 **3. Test des Demandes de Service**

### **Configuration du Thread Group**
- **Nom** : `Demandes de Service`
- **Nombre d'utilisateurs** : 8
- **Ramp-up** : 8 secondes
- **Durée** : 180 secondes

### **Endpoints à Tester**

#### **3.1 Création de Demande**
```
POST /services
URL: http://localhost:4000/api/v1/services
Headers: 
  Content-Type: application/json
  Authorization: Bearer ${token}

Body (JSON):
{
  "title": "Demande Test ${__Random(1,1000)}",
  "description": "Description de la demande de test",
  "category": "Plomberie",
  "budget": ${__Random(80,300)},
  "date": "2024-12-15T10:00:00.000Z",
  "location": {
    "address": "456 Rue Client, Paris",
    "latitude": 48.8566,
    "longitude": 2.3522
  }
}
```

#### **3.2 Liste des Demandes**
```
GET /services
URL: http://localhost:4000/api/v1/services
Headers: Authorization: Bearer ${token}
```

#### **3.3 Recherche de Demandes**
```
GET /services/search
URL: http://localhost:4000/api/v1/services/search?q=plomberie&category=Plomberie&minBudget=50&maxBudget=200
Headers: Authorization: Bearer ${token}
```

---

## 🔍 **4. Test de Recherche et Filtres**

### **Configuration du Thread Group**
- **Nom** : `Recherche et Filtres`
- **Nombre d'utilisateurs** : 15
- **Ramp-up** : 15 secondes
- **Durée** : 90 secondes

### **Endpoints à Tester**

#### **4.1 Recherche de Services**
```
GET /provider-services
URL: http://localhost:4000/api/v1/provider-services?category=Plomberie&maxPrice=100&latitude=48.8566&longitude=2.3522&radius=5
Headers: Authorization: Bearer ${token}
```

#### **4.2 Services à Proximité**
```
GET /services/nearby
URL: http://localhost:4000/api/v1/services/nearby?latitude=48.8566&longitude=2.3522&radius=10
Headers: Authorization: Bearer ${token}
```

#### **4.3 Catégories de Services**
```
GET /services/categories
URL: http://localhost:4000/api/v1/services/categories
Headers: Authorization: Bearer ${token}
```

---

## 📊 **5. Test de Charge et Performance**

### **Configuration du Thread Group**
- **Nom** : `Test de Charge`
- **Nombre d'utilisateurs** : 50
- **Ramp-up** : 60 secondes
- **Durée** : 300 secondes

### **Scénario de Test**
1. **Phase 1** (0-60s) : Montée en charge progressive
2. **Phase 2** (60-240s) : Charge constante
3. **Phase 3** (240-300s) : Décroissance

---

## 📈 **6. Configuration des Listeners**

### **6.1 View Results Tree**
- **Nom** : `Arbre des Résultats`
- **Filtre** : Afficher seulement les erreurs

### **6.2 Aggregate Report**
- **Nom** : `Rapport Agregé`
- **Métriques** : Temps de réponse, débit, erreurs

### **6.3 Response Time Graph**
- **Nom** : `Graphique Temps de Réponse`
- **Échelle** : Temps en millisecondes

### **6.4 Summary Report**
- **Nom** : `Rapport Sommaire`
- **Format** : CSV pour analyse externe

---

## ⚙️ **7. Variables et Configuration**

### **7.1 Variables Globales**
```
baseUrl = http://localhost:4000/api/v1
apiVersion = v1
timeout = 10000
```

### **7.2 Variables d'Environnement**
```
# Développement
dev.baseUrl = http://localhost:4000/api/v1
dev.port = 4000

# Production
prod.baseUrl = https://your-production-api.com/api/v1
prod.port = 443
```

### **7.3 Configuration HTTP Request Defaults**
- **Protocole** : http
- **Nom du serveur** : localhost
- **Port** : 4000
- **Timeout de connexion** : 5000
- **Timeout de réponse** : 10000

---

## 🚨 **8. Gestion des Erreurs**

### **8.1 Assertions de Réponse**
- **Code de statut** : 200, 201, 204
- **Temps de réponse** : < 2000ms
- **Contenu JSON** : Vérifier la structure

### **8.2 Gestion des Timeouts**
- **Timeout de connexion** : 5000ms
- **Timeout de réponse** : 10000ms
- **Retry automatique** : 3 tentatives

### **8.3 Logging des Erreurs**
- **Fichier de log** : `jmeter_errors.log`
- **Niveau** : ERROR, WARN

---

## 📊 **9. Métriques de Performance**

### **9.1 Temps de Réponse**
- **P50** : < 500ms
- **P90** : < 1000ms
- **P95** : < 2000ms
- **P99** : < 5000ms

### **9.2 Débit (Throughput)**
- **Requêtes par seconde** : > 100 req/s
- **Utilisateurs simultanés** : > 50

### **9.3 Taux d'Erreur**
- **Erreurs HTTP** : < 1%
- **Timeouts** : < 0.5%

---

## 🧪 **10. Exécution des Tests**

### **10.1 Test de Fumée (Smoke Test)**
```bash
# Test rapide avec 1 utilisateur
jmeter -n -t weServeApp_TestPlan.jmx -l smoke_test_results.jtl -e -o smoke_test_report
```

### **10.2 Test de Charge**
```bash
# Test de charge complet
jmeter -n -t weServeApp_TestPlan.jmx -l load_test_results.jtl -e -o load_test_report
```

### **10.3 Test de Stress**
```bash
# Test de stress avec plus d'utilisateurs
jmeter -n -t weServeApp_TestPlan.jmx -l stress_test_results.jtl -e -o stress_test_report
```

---

## 📋 **11. Checklist de Préparation**

### **11.1 Environnement**
- [ ] Backend démarré sur le port 4000
- [ ] Base de données accessible
- [ ] CORS configuré correctement
- [ ] Variables d'environnement définies

### **11.2 Données de Test**
- [ ] Comptes utilisateurs de test créés
- [ ] Services de test disponibles
- [ ] Demandes de test existantes
- [ ] Photos de test uploadées

### **11.3 Configuration JMeter**
- [ ] Plan de test créé
- [ ] Variables configurées
- [ ] Listeners ajoutés
- [ ] Assertions définies

---

## 📊 **12. Analyse des Résultats**

### **12.1 Rapports Automatiques**
- **HTML Report** : Généré automatiquement par JMeter
- **CSV Export** : Pour analyse dans Excel/Google Sheets
- **Graphiques** : Temps de réponse, débit, erreurs

### **12.2 Métriques Clés**
- **Latence** : Temps de réponse moyen
- **Débit** : Requêtes par seconde
- **Erreurs** : Taux d'erreur et types
- **Ressources** : CPU, mémoire, réseau

---

## 🔧 **13. Dépannage**

### **13.1 Erreurs Communes**
- **Connection refused** : Vérifier que le backend est démarré
- **CORS errors** : Vérifier la configuration CORS
- **Authentication failed** : Vérifier les tokens JWT
- **Timeout** : Augmenter les timeouts ou optimiser le backend

### **13.2 Optimisations**
- **Pool de connexions** : Configurer la taille du pool
- **Cache** : Activer le cache des réponses
- **Compression** : Activer la compression gzip
- **CDN** : Utiliser un CDN pour les assets statiques

---

## 📚 **14. Ressources Supplémentaires**

### **14.1 Documentation**
- [Documentation JMeter officielle](https://jmeter.apache.org/usermanual/index.html)
- [Guide des bonnes pratiques](https://jmeter.apache.org/usermanual/best-practices.html)
- [Plugins JMeter](https://jmeter.apache.org/usermanual/component_reference.html)

### **14.2 Outils Complémentaires**
- **Grafana** : Visualisation des métriques
- **InfluxDB** : Stockage des données de performance
- **Postman** : Tests d'API individuels
- **New Relic** : Monitoring en production

---

## 🎯 **15. Prochaines Étapes**

1. **Créer le plan de test JMeter** basé sur ce guide
2. **Configurer l'environnement de test** avec des données de test
3. **Exécuter les tests de fumée** pour valider la configuration
4. **Lancer les tests de charge** pour identifier les goulots d'étranglement
5. **Analyser les résultats** et optimiser le backend
6. **Mettre en place le monitoring** en production

---

**🚀 Votre application weServeApp est maintenant prête pour des tests de performance complets avec JMeter !** 