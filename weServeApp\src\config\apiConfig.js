// Configuration de l'API
export const API_BASE_URL = __DEV__ 
  ? 'http://10.0.2.2:5000/api/v1'  // URL de développement
  : 'https://your-production-api.com/api/v1'; // URL de production

// Timeout des requêtes API (en millisecondes)
export const API_TIMEOUT = 10000; // 10 secondes

// Configuration des en-têtes par défaut
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Configuration des endpoints d'authentification
export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  SIGNUP: '/auth/signup',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  ME: '/auth/me',
};

// Configuration des endpoints des services
export const SERVICE_ENDPOINTS = {
  SERVICES: '/services',
  REQUESTS: '/requests',
  PROVIDERS: '/providers',
  CATEGORIES: '/categories',
};

// Configuration des endpoints des utilisateurs
export const USER_ENDPOINTS = {
  PROFILE: '/users/profile',
  UPDATE: '/users/update',
  DELETE: '/users/delete',
};

// Configuration des codes de statut HTTP
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};

// Configuration des messages d'erreur
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Erreur de connexion réseau. Vérifiez votre connexion internet.',
  TIMEOUT_ERROR: 'Délai d\'attente dépassé. Veuillez réessayer.',
  SERVER_ERROR: 'Erreur du serveur. Veuillez réessayer plus tard.',
  UNAUTHORIZED: 'Non autorisé. Veuillez vous connecter.',
  FORBIDDEN: 'Accès interdit.',
  NOT_FOUND: 'Ressource non trouvée.',
  VALIDATION_ERROR: 'Données invalides. Veuillez vérifier vos informations.',
  UNKNOWN_ERROR: 'Une erreur inconnue s\'est produite.',
};

// Configuration du logging des appels API
export const API_LOGGING = {
  ENABLED: __DEV__, // Activer le logging seulement en développement
  LOG_REQUESTS: true,
  LOG_RESPONSES: true,
  LOG_ERRORS: true,
};

// Fonction pour logger les appels API
export const logApiCall = (method, endpoint, data = null) => {
  if (!API_LOGGING.ENABLED || !API_LOGGING.LOG_REQUESTS) return;
  
  console.log(`🌐 API ${method.toUpperCase()}: ${endpoint}`);
  if (data) {
    console.log('📤 Données envoyées:', data);
  }
};

// Fonction pour logger les réponses API
export const logApiResponse = (endpoint, response) => {
  if (!API_LOGGING.ENABLED || !API_LOGGING.LOG_RESPONSES) return;
  
  console.log(`✅ Réponse API ${endpoint}:`, response);
};

// Fonction pour logger les erreurs API
export const logApiError = (endpoint, error) => {
  if (!API_LOGGING.ENABLED || !API_LOGGING.LOG_ERRORS) return;
  
  console.error(`❌ Erreur API ${endpoint}:`, error);
};

// Configuration des tentatives de reconnexion
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 seconde
  RETRY_MULTIPLIER: 2, // Multiplier le délai par 2 à chaque tentative
};

// Configuration de la mise en cache
export const CACHE_CONFIG = {
  ENABLED: true,
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  MAX_SIZE: 100, // Nombre maximum d'éléments en cache
};
