@echo off
echo ========================================
echo    Test JMeter avec Auth pour weServeApp
echo ========================================
echo.

echo 🔍 Vérification de JMeter...
echo.

REM Essayer de trouver JMeter
set JMETER_FOUND=false

REM Vérifier dans le PATH
where jmeter >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ JMeter trouvé dans le PATH
    set JMETER_CMD=jmeter
    set JMETER_FOUND=true
    goto run_test
)

REM Emplacements courants
if exist "C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files
    set JMETER_FOUND=true
    goto run_test
)

if exist "C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files (x86)
    set JMETER_FOUND=true
    goto run_test
)

if exist "%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Downloads
    set JMETER_FOUND=true
    goto run_test
)

if exist "%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé sur le Bureau
    set JMETER_FOUND=true
    goto run_test
)

if exist "C:\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans C:\
    set JMETER_FOUND=true
    goto run_test
)

if "%JMETER_FOUND%"=="false" (
    echo ❌ JMeter non trouvé!
    echo.
    echo Veuillez:
    echo 1. Installer JMeter depuis: https://jmeter.apache.org/download_jmeter.cgi
    echo 2. Ou spécifier le chemin manuellement
    echo.
    set /p custom_path="Chemin complet vers jmeter.bat: "
    if exist "%custom_path%" (
        set JMETER_CMD="%custom_path%"
        echo ✅ Chemin personnalisé accepté
        set JMETER_FOUND=true
        goto run_test
    ) else (
        echo ❌ Fichier non trouvé
        pause
        exit /b 1
    )
)

:run_test
echo.
echo 🎯 Test avec Authentification: %JMETER_CMD%
echo.

echo 📋 Ce test va vérifier:
echo 1. ✅ Endpoint public accessible (GET /auth/login)
echo 2. ❌ Endpoint protégé refuse l'accès sans token (GET /services/categories)
echo 3. ✅ Documentation API accessible (GET /api-docs)
echo.

echo ⚠️  IMPORTANT: Assurez-vous que votre backend weServeApp est démarré!
echo    - Port: 5000
echo    - Endpoint: /api/v1
echo.

echo 🔍 Comportement attendu:
echo - /auth/login: 200 OK (endpoint public)
echo - /services/categories: 401 Unauthorized (endpoint protégé)
echo - /api-docs: 200 OK (documentation)
echo.

set /p confirm="Votre backend est-il démarré? (o/n): "
if /i "%confirm%"=="o" (
    echo.
    echo 🚀 Démarrage du test avec authentification...
    echo.
    
    REM Créer le dossier de résultats s'il n'existe pas
    if not exist "jmeter_results" mkdir jmeter_results
    
    REM Exécuter le test avec auth
    %JMETER_CMD% -n -t weServeApp_TestPlan_WithAuth.jmx -l jmeter_results/auth_test_results.jtl -e -o jmeter_results/auth_test_report
    
    if %errorlevel% equ 0 (
        echo.
        echo ✅ Test avec authentification terminé avec succès!
        echo.
        echo 📊 Résultats disponibles dans: jmeter_results/auth_test_report/
        echo 📝 Logs disponibles dans: jmeter_results/auth_test_results.jtl
        echo.
        echo 🌐 Ouvrez jmeter_results/auth_test_report/index.html dans votre navigateur
        echo pour voir les résultats détaillés.
        echo.
        echo 🎯 Résultats attendus:
        echo - Test Endpoint Public: ✅ 200 OK
        echo - Test Endpoint Protégé: ✅ 401 Unauthorized (comportement correct!)
        echo - Test API Docs: ✅ 200 OK
        echo.
        echo 🎉 Votre API fonctionne correctement avec l'authentification!
        echo.
    ) else (
        echo.
        echo ❌ Erreur lors de l'exécution du test
        echo.
        echo 🔍 Vérifiez:
        echo 1. Que votre backend weServeApp est démarré sur le port 5000
        echo 2. Que les endpoints sont accessibles
        echo 3. Que CORS est configuré correctement
        echo.
        echo 💡 Testez d'abord avec votre navigateur:
        echo http://localhost:5000/api/v1/auth/login
        echo http://localhost:5000/api/v1/services/categories
        echo http://localhost:5000/api-docs
        echo.
    )
) else (
    echo.
    echo ⚠️  Veuillez démarrer votre backend weServeApp d'abord
    echo.
    echo 📋 Instructions:
    echo 1. Ouvrez un terminal dans le dossier de votre backend
    echo 2. Exécutez: npm start ou npm run dev
    echo 3. Vérifiez que le serveur démarre sur le port 5000
    echo 4. Relancez ce script
    echo.
)

echo Appuyez sur une touche pour continuer...
pause >nul 