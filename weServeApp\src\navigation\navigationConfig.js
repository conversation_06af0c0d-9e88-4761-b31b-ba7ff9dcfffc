import { Platform } from 'react-native';

// Configuration de navigation pour résoudre l'erreur topInsetsChange sur Expo SDK 52
export const navigationConfig = {
  // Configuration générale
  general: {
    // Désactiver enableScreens sur Android
    enableScreens: Platform.OS === 'ios',
    
    // Configuration des transitions
    transitionSpec: {
      open: {
        animation: 'timing',
        config: {
          duration: 300,
          easing: 'easeInOut',
        },
      },
      close: {
        animation: 'timing',
        config: {
          duration: 300,
          easing: 'easeInOut',
        },
      },
    },
  },
  
  // Configuration des écrans de pile
  stack: {
    screenOptions: {
      headerShown: false,
      gestureEnabled: true,
      // Utiliser des transitions simples
      cardStyleInterpolator: ({ current, layouts }) => ({
        cardStyle: {
          transform: [
            {
              translateX: current.progress.interpolate({
                inputRange: [0, 1],
                outputRange: [layouts.screen.width, 0],
              }),
            },
          ],
        },
      }),
      // Éviter les animations problématiques
      animationEnabled: Platform.OS === 'ios',
    },
  },
  
  // Configuration des onglets
  tabs: {
    screenOptions: {
      headerShown: false,
      // Configuration des onglets
      tabBarStyle: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
        height: Platform.OS === 'ios' ? 88 : 60,
        paddingBottom: Platform.OS === 'ios' ? 20 : 8,
        paddingTop: 8,
      },
      tabBarActiveTintColor: '#2563EB',
      tabBarInactiveTintColor: '#6B7280',
      tabBarLabelStyle: {
        fontSize: 12,
        fontWeight: '500',
      },
    },
  },
  
  // Configuration des modales
  modal: {
    screenOptions: {
      presentation: 'modal',
      animationEnabled: Platform.OS === 'ios',
      gestureEnabled: true,
    },
  },
};

// Fonction pour obtenir la configuration selon le type de navigation
export const getNavigationConfig = (type = 'general') => {
  return navigationConfig[type] || navigationConfig.general;
};

// Configuration spécifique pour éviter l'erreur topInsetsChange
export const safeAreaConfig = {
  // Désactiver les fonctionnalités problématiques
  enableScreens: Platform.OS === 'ios',
  
  // Configuration des safe areas
  screenOptions: {
    headerShown: false,
    gestureEnabled: true,
    // Utiliser des transitions simples
    cardStyleInterpolator: ({ current, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
  },
};
