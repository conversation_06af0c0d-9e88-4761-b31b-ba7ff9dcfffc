import * as providerServiceService from '../services/providerServiceService.js';
import { validateProviderServiceData } from '../utils/joiSchemas.js';

// Créer un nouveau service de prestataire
export const createProviderService = async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un prestataire
    if (req.user.role !== 'provider') {
      return res.status(403).json({
        success: false,
        message: 'Seuls les prestataires peuvent créer des services'
      });
    }

    // Valider les données
    const { error, value } = validateProviderServiceData(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: error.details.map(detail => detail.message)
      });
    }

    // Préparer les données du service
    const serviceData = {
      ...value,
      providerId: req.user.id,
      location: {
        address: value.location.address,
        coordinates: {
          type: 'Point',
          coordinates: [value.location.longitude, value.location.latitude]
        }
      }
    };

    // Créer le service
    const providerService = await providerServiceService.createProviderService(
      serviceData,
      req.files?.photos || []
    );

    res.status(201).json({
      success: true,
      message: 'Service créé avec succès',
      data: {
        id: providerService._id,
        title: providerService.title,
        category: providerService.category,
        price: providerService.price,
        status: providerService.status,
        createdAt: providerService.createdAt
      }
    });
  } catch (error) {
    console.error('Create provider service error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du service',
      error: error.message
    });
  }
};

// Obtenir tous les services actifs (pour les clients)
export const getAllActiveServices = async (req, res) => {
  try {
    const { category, maxPrice, page, limit, latitude, longitude, radius } = req.query;
    
    const filters = {
      category,
      maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
      page: page ? parseInt(page) : 1,
      limit: limit ? parseInt(limit) : 20
    };

    // Ajouter la localisation si fournie
    if (latitude && longitude) {
      filters.location = {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        radius: radius ? parseFloat(radius) : 10
      };
    }

    const result = await providerServiceService.getAllActiveServices(filters);

    res.status(200).json({
      success: true,
      message: 'Services récupérés avec succès',
      data: result.services,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('Get all active services error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des services',
      error: error.message
    });
  }
};

// Obtenir un service par ID
export const getProviderServiceById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const service = await providerServiceService.getProviderServiceById(id);
    
    res.status(200).json({
      success: true,
      message: 'Service récupéré avec succès',
      data: service
    });
  } catch (error) {
    console.error('Get provider service by ID error:', error);
    
    if (error.message === 'Service non trouvé') {
      return res.status(404).json({
        success: false,
        message: 'Service non trouvé'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du service',
      error: error.message
    });
  }
};

// Obtenir les services du prestataire connecté
export const getMyServices = async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un prestataire
    if (req.user.role !== 'provider') {
      return res.status(403).json({
        success: false,
        message: 'Seuls les prestataires peuvent voir leurs services'
      });
    }

    const { status } = req.query;
    const services = await providerServiceService.getServicesByProvider(req.user.id, status);
    
    res.status(200).json({
      success: true,
      message: 'Services récupérés avec succès',
      data: services
    });
  } catch (error) {
    console.error('Get my services error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des services',
      error: error.message
    });
  }
};

// Mettre à jour un service
export const updateProviderService = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Vérifier que l'utilisateur est un prestataire
    if (req.user.role !== 'provider') {
      return res.status(403).json({
        success: false,
        message: 'Seuls les prestataires peuvent modifier des services'
      });
    }

    // Valider les données de mise à jour
    const { error, value } = validateProviderServiceData(req.body, true);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: error.details.map(detail => detail.message)
      });
    }

    // Préparer les données de mise à jour
    let updateData = { ...value };
    
    // Si la localisation est mise à jour, reformater les coordonnées
    if (value.location) {
      updateData.location = {
        address: value.location.address,
        coordinates: {
          type: 'Point',
          coordinates: [value.location.longitude, value.location.latitude]
        }
      };
    }

    const updatedService = await providerServiceService.updateProviderService(
      id,
      updateData,
      req.user.id
    );

    res.status(200).json({
      success: true,
      message: 'Service mis à jour avec succès',
      data: updatedService
    });
  } catch (error) {
    console.error('Update provider service error:', error);
    
    if (error.message === 'Service non trouvé') {
      return res.status(404).json({
        success: false,
        message: 'Service non trouvé'
      });
    }
    
    if (error.message === 'Non autorisé à modifier ce service') {
      return res.status(403).json({
        success: false,
        message: 'Non autorisé à modifier ce service'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du service',
      error: error.message
    });
  }
};

// Supprimer un service
export const deleteProviderService = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Vérifier que l'utilisateur est un prestataire
    if (req.user.role !== 'provider') {
      return res.status(403).json({
        success: false,
        message: 'Seuls les prestataires peuvent supprimer des services'
      });
    }

    const result = await providerServiceService.deleteProviderService(id, req.user.id);
    
    res.status(200).json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Delete provider service error:', error);
    
    if (error.message === 'Service non trouvé') {
      return res.status(404).json({
        success: false,
        message: 'Service non trouvé'
      });
    }
    
    if (error.message === 'Non autorisé à supprimer ce service') {
      return res.status(403).json({
        success: false,
        message: 'Non autorisé à supprimer ce service'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du service',
      error: error.message
    });
  }
};

// Rechercher des services
export const searchServices = async (req, res) => {
  try {
    const { q, category, maxPrice, page, limit, latitude, longitude, radius } = req.query;
    
    const searchCriteria = {
      q,
      category,
      maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
      page: page ? parseInt(page) : 1,
      limit: limit ? parseInt(limit) : 20
    };

    // Ajouter la localisation si fournie
    if (latitude && longitude) {
      searchCriteria.location = {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        radius: radius ? parseFloat(radius) : 10
      };
    }

    const result = await providerServiceService.searchServices(searchCriteria);

    res.status(200).json({
      success: true,
      message: 'Recherche effectuée avec succès',
      data: result.services,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('Search services error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la recherche',
      error: error.message
    });
  }
};

// Obtenir les services à proximité
export const getNearbyServices = async (req, res) => {
  try {
    const { latitude, longitude, radius = 10, category } = req.query;
    
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        message: 'Latitude et longitude sont requises'
      });
    }

    const services = await providerServiceService.getNearbyServices(
      parseFloat(latitude),
      parseFloat(longitude),
      parseFloat(radius),
      category
    );

    res.status(200).json({
      success: true,
      message: 'Services à proximité récupérés avec succès',
      data: services
    });
  } catch (error) {
    console.error('Get nearby services error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des services à proximité',
      error: error.message
    });
  }
};

// Obtenir les statistiques des services du prestataire
export const getMyServiceStats = async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un prestataire
    if (req.user.role !== 'provider') {
      return res.status(403).json({
        success: false,
        message: 'Seuls les prestataires peuvent voir leurs statistiques'
      });
    }

    const stats = await providerServiceService.getProviderServiceStats(req.user.id);
    
    res.status(200).json({
      success: true,
      message: 'Statistiques récupérées avec succès',
      data: stats
    });
  } catch (error) {
    console.error('Get my service stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
};
