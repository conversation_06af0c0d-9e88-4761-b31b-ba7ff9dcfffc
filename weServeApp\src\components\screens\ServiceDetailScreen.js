import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import Button from '../common/Card';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';

const ServiceDetailScreen = ({ navigation, route }) => {
  const { service, request } = route.params;
  const isService = !!service;
  const data = service || request;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons 
              name={isService ? 'construct' : 'document-text'} 
              size={40} 
              color={isService ? COLORS.primary : COLORS.secondary} 
            />
          </View>
          <Text style={styles.title}>{data?.title || 'Titre du service'}</Text>
          <Text style={styles.category}>{data?.category || 'Catégorie'}</Text>
        </View>

        <View style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>
              {data?.description || 'Description détaillée du service ou de la demande...'}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations</Text>
            <View style={styles.infoGrid}>
              {isService ? (
                <>
                  <View style={styles.infoItem}>
                    <Ionicons name="wallet" size={20} color={COLORS.primary} />
                    <Text style={styles.infoLabel}>Prix</Text>
                    <Text style={styles.infoValue}>{data?.price || '0'}€/h</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Ionicons name="star" size={20} color={COLORS.warning} />
                    <Text style={styles.infoLabel}>Note</Text>
                    <Text style={styles.infoValue}>{data?.rating || '0'}/5</Text>
                  </View>
                </>
              ) : (
                <>
                  <View style={styles.infoItem}>
                    <Ionicons name="wallet" size={20} color={COLORS.accent} />
                    <Text style={styles.infoLabel}>Budget</Text>
                    <Text style={styles.infoValue}>{data?.budget || '0'}€</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Ionicons name="time" size={20} color={COLORS.warning} />
                    <Text style={styles.infoLabel}>Statut</Text>
                    <Text style={styles.infoValue}>{data?.status || 'En attente'}</Text>
                  </View>
                </>
              )}
              <View style={styles.infoItem}>
                <Ionicons name="location" size={20} color={COLORS.secondary} />
                <Text style={styles.infoLabel}>Localisation</Text>
                <Text style={styles.infoValue}>
                  {data?.location?.address || data?.location || 'Localisation non spécifiée'}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Actions</Text>
            <View style={styles.actionsContainer}>
              <Button
                title={isService ? 'Contacter' : 'Accepter'}
                variant="primary"
                style={styles.actionButton}
                onPress={() => Alert.alert('Action', 'Fonctionnalité à implémenter')}
              />
              <Button
                title="Voir le profil"
                variant="outline"
                style={styles.actionButton}
                onPress={() => Alert.alert('Profil', 'Fonctionnalité à implémenter')}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    padding: SIZES.padding * 1.5,
    backgroundColor: COLORS.white,
    marginBottom: SIZES.medium,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.medium,
    ...SHADOWS.medium,
  },
  title: {
    fontSize: SIZES.extraLarge,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    textAlign: 'center',
    marginBottom: SIZES.base,
  },
  category: {
    fontSize: SIZES.large,
    fontFamily: FONTS.medium,
    color: COLORS.gray,
    textAlign: 'center',
  },
  content: {
    padding: SIZES.padding,
  },
  section: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: SIZES.medium,
    ...SHADOWS.small,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: SIZES.medium,
  },
  description: {
    fontSize: SIZES.font,
    fontFamily: FONTS.regular,
    color: COLORS.dark,
    lineHeight: 22,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  infoItem: {
    width: '48%',
    alignItems: 'center',
    padding: SIZES.medium,
    backgroundColor: COLORS.light,
    borderRadius: SIZES.radius,
    marginBottom: SIZES.medium,
  },
  infoLabel: {
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.gray,
    marginTop: SIZES.base,
    marginBottom: SIZES.base,
  },
  infoValue: {
    fontSize: SIZES.font,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 0.48,
  },
});

export default ServiceDetailScreen;
