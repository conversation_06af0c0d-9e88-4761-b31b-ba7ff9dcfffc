import { Router } from 'express';
import { authenticate } from '../middlewares/auth.js';
import { authorizeRoles } from '../middlewares/roles.js';
import { validate } from '../middlewares/validate.js';
import { reviewCreateSchema } from '../utils/joiSchemas.js';
import { create } from '../controllers/reviewController.js';

const router = Router();

router.post('/', authenticate, authorizeRoles('client'), validate(reviewCreateSchema), create);

export default router; 