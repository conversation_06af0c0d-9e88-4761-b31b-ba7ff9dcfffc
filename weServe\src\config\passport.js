import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as FacebookStrategy } from 'passport-facebook';
import User from '../models/User.js';

function ensureProfileEmail(profile) {
  if (profile.emails && profile.emails.length > 0) return profile.emails[0].value;
  return undefined;
}

if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: process.env.GOOGLE_CALLBACK_URL || '/api/v1/auth/google/callback'
      },
      async (_accessToken, _refreshToken, profile, done) => {
        try {
          const email = ensureProfileEmail(profile);
          const providerId = profile.id;
          const name = profile.displayName || 'Google User';
          const avatarUrl = profile.photos && profile.photos[0] ? profile.photos[0].value : undefined;
          let user = await User.findOne({ $or: [{ email }, { authProviderId: providerId }] });
          if (!user) {
            user = await User.create({
              name,
              email,
              avatarUrl,
              role: 'client',
              authProvider: 'google',
              authProviderId: providerId
            });
          } else {
            if (!user.authProvider) user.authProvider = 'google';
            if (!user.authProviderId) user.authProviderId = providerId;
            if (avatarUrl && !user.avatarUrl) user.avatarUrl = avatarUrl;
            await user.save();
          }
          return done(null, user);
        } catch (err) {
          return done(err);
        }
      }
    )
  );
}

if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
  passport.use(
    new FacebookStrategy(
      {
        clientID: process.env.FACEBOOK_APP_ID,
        clientSecret: process.env.FACEBOOK_APP_SECRET,
        callbackURL: process.env.FACEBOOK_CALLBACK_URL || '/api/v1/auth/facebook/callback',
        profileFields: ['id', 'displayName', 'emails', 'photos']
      },
      async (_accessToken, _refreshToken, profile, done) => {
        try {
          const email = ensureProfileEmail(profile);
          const providerId = profile.id;
          const name = profile.displayName || 'Facebook User';
          const avatarUrl = profile.photos && profile.photos[0] ? profile.photos[0].value : undefined;
          let user = await User.findOne({ $or: [{ email }, { authProviderId: providerId }] });
          if (!user) {
            user = await User.create({
              name,
              email,
              avatarUrl,
              role: 'client',
              authProvider: 'facebook',
              authProviderId: providerId
            });
          } else {
            if (!user.authProvider) user.authProvider = 'facebook';
            if (!user.authProviderId) user.authProviderId = providerId;
            if (avatarUrl && !user.avatarUrl) user.avatarUrl = avatarUrl;
            await user.save();
          }
          return done(null, user);
        } catch (err) {
          return done(err);
        }
      }
    )
  );
}

export default passport; 