import Review from '../models/Review.js';
import User from '../models/User.js';
import ServiceRequest from '../models/ServiceRequest.js';

export async function createReview({ serviceRequestId, clientId, providerId, rating, comment }) {
  const sr = await ServiceRequest.findById(serviceRequestId);
  if (!sr) throw Object.assign(new Error('Service request not found'), { status: 404 });
  if (sr.status !== 'completed') throw Object.assign(new Error('Can only review completed requests'), { status: 400 });
  if (sr.clientId.toString() !== clientId) throw Object.assign(new Error('Only owner can review'), { status: 403 });
  if (sr.providerId?.toString() !== providerId) throw Object.assign(new Error('Invalid provider'), { status: 400 });

  const review = await Review.create({ serviceRequestId, clientId, providerId, rating, comment });

  // update provider rating
  const provider = await User.findById(providerId);
  const total = provider.totalRatings + 1;
  const avg = (provider.averageRating * provider.totalRatings + rating) / total;
  provider.totalRatings = total;
  provider.averageRating = avg;
  await provider.save();

  return review;
} 