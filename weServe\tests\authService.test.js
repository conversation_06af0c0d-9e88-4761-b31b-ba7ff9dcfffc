import { jest, describe, test, expect, beforeEach } from '@jest/globals';

await jest.unstable_mockModule('../src/models/User.js', () => ({
  default: { findOne: jest.fn(), create: jest.fn() }
}));
await jest.unstable_mockModule('../src/utils/email.js', () => ({ sendEmail: jest.fn() }));
await jest.unstable_mockModule('../src/utils/sms.js', () => ({ sendSms: jest.fn() }));

const { default: User } = await import('../src/models/User.js');
const auth = await import('../src/services/authService.js');

describe('authService', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  test('signup creates user and returns tokens', async () => {
    User.findOne.mockResolvedValue(null);
    User.create.mockResolvedValue({ _id: 'u1', save: jest.fn(), role: 'client' });
    const res = await auth.signup({ name: 'A', email: '<EMAIL>', password: 'secret', role: 'client' });
    expect(res.user._id).toBe('u1');
    expect(res.accessToken).toBeDefined();
    expect(res.refreshToken).toBeDefined();
  });

  test('login fails with invalid', async () => {
    User.findOne.mockResolvedValue(null);
    await expect(auth.login({ email: '<EMAIL>', password: 'nope' })).rejects.toBeTruthy();
  });
}); 