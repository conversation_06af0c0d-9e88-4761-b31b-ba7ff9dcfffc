import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import Card from '../common/Card';
import Button from '../common/Button';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { SERVICE_CATEGORIES } from '../../constants/categories';
import { useAuthStore } from '../../stores/authStore';
import servicesService from '../../services/servicesService';

const { width } = Dimensions.get('window');

// Fonction de validation du format de date ISO 8601
const isValidDateFormat = (dateString) => {
  // Format ISO 8601 simple (YYYY-MM-DD)
  const isoDateRegex = /^\d{4}-\d{2}-\d{2}$/;
  
  if (isoDateRegex.test(dateString)) {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && date.toISOString().split('T')[0] === dateString;
  }
  
  return false;
};

const CreateServiceRequestScreen = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const serviceId = route.params?.serviceId; // Si on vient d'un service spécifique
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: null,
    budget: '',
    preferredDate: '',
    location: '',
    urgency: 'normal', // low, normal, high, urgent
    contactPhone: user?.phone || '',
    contactEmail: user?.email || '',
    additionalInfo: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Effacer l'erreur quand l'utilisateur commence à taper
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'La description doit contenir au moins 10 caractères';
    }
    if (!formData.category) {
      newErrors.category = 'Veuillez sélectionner une catégorie';
    }
    if (!formData.budget || parseFloat(formData.budget) <= 0) {
      newErrors.budget = 'Le budget doit être supérieur à 0';
    }
    if (!formData.preferredDate.trim()) {
      newErrors.preferredDate = 'La date souhaitée est requise';
    } else if (!isValidDateFormat(formData.preferredDate)) {
      newErrors.preferredDate = 'La date doit être au format ISO 8601 (ex: 2024-12-15)';
    }
    if (!formData.location.trim()) {
      newErrors.location = 'La localisation est requise';
    }
    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Le numéro de téléphone est requis';
    }
    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = 'L\'email est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert('Erreur', 'Veuillez corriger les erreurs dans le formulaire');
      return;
    }

    setIsSubmitting(true);
    try {
      // Préparation des données pour l'API
      const requestData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category.name, // Envoyer juste le nom de la catégorie
        budget: parseFloat(formData.budget),
        date: formData.preferredDate,
        location: {
          address: formData.location.trim(),
          // TODO: Ajouter la géolocalisation réelle
          latitude: 48.8566, // Paris par défaut
          longitude: 2.3522
        },
        urgency: formData.urgency,
        contactPhone: formData.contactPhone.trim(),
        contactEmail: formData.contactEmail.trim(),
        additionalInfo: formData.additionalInfo.trim()
      };

      // Log des données envoyées
      console.log('📤 Données envoyées à l\'API:', JSON.stringify(requestData, null, 2));
      
      // Appel à l'API
      const result = await servicesService.createServiceRequest(requestData);
      
      // Log de la réponse
      console.log('📥 Réponse de l\'API:', JSON.stringify(result, null, 2));
      
      if (result.success) {
        Alert.alert(
          'Succès !',
          'Votre demande de service a été envoyée avec succès',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Services')
            }
          ]
        );
      } else {
        const errorMessage = result.message || 'Une erreur est survenue lors de l\'envoi de la demande';
        console.log('❌ Erreur API:', errorMessage);
        console.log('❌ Détails de l\'erreur:', result.error);
        Alert.alert('Erreur de Validation', errorMessage);
      }
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur de connexion est survenue. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={COLORS.white} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Demander un service</Text>
      <View style={styles.headerIcon}>
        <Ionicons name="add-circle" size={30} color={COLORS.white} />
      </View>
    </View>
  );

  const renderCategorySelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Type de service *</Text>
      <View style={styles.categoriesGrid}>
        {SERVICE_CATEGORIES.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryCard,
              formData.category?.id === category.id && styles.categoryCardSelected
            ]}
            onPress={() => updateFormData('category', category)}
          >
            <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
              <Ionicons name={category.icon} size={24} color={COLORS.white} />
            </View>
            <Text style={[
              styles.categoryText,
              formData.category?.id === category.id && styles.categoryTextSelected
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {errors.category && <Text style={styles.errorText}>{errors.category}</Text>}
    </View>
  );

  const renderUrgencySelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Niveau d'urgence</Text>
      <Text style={styles.sectionSubtitle}>
        Indiquez l'urgence de votre demande
      </Text>
      
      <View style={styles.urgencyGrid}>
        {[
          { key: 'low', label: 'Faible', icon: 'time', color: COLORS.success },
          { key: 'normal', label: 'Normale', icon: 'calendar', color: COLORS.primary },
          { key: 'high', label: 'Élevée', icon: 'warning', color: COLORS.warning },
          { key: 'urgent', label: 'Urgente', icon: 'alert-circle', color: COLORS.danger }
        ].map((urgency) => (
          <TouchableOpacity
            key={urgency.key}
            style={[
              styles.urgencyCard,
              formData.urgency === urgency.key && styles.urgencyCardSelected
            ]}
            onPress={() => updateFormData('urgency', urgency.key)}
          >
            <View style={[styles.urgencyIcon, { backgroundColor: urgency.color }]}>
              <Ionicons name={urgency.icon} size={20} color={COLORS.white} />
            </View>
            <Text style={[
              styles.urgencyText,
              formData.urgency === urgency.key && styles.urgencyTextSelected
            ]}>
              {urgency.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderFormFields = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Détails de la demande</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Titre de la demande *</Text>
        <TextInput
          style={[styles.textInput, errors.title && styles.inputError]}
          value={formData.title}
          onChangeText={(text) => updateFormData('title', text)}
          placeholder="Ex: Réparation fuite d'eau, Installation prise électrique..."
          placeholderTextColor={COLORS.gray}
        />
        {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
      </View>

              <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Description détaillée *</Text>
          <TextInput
            style={[styles.textArea, errors.description && styles.inputError]}
            value={formData.description}
            onChangeText={(text) => updateFormData('description', text)}
            placeholder="Décrivez votre problème, vos besoins, les détails importants..."
            placeholderTextColor={COLORS.gray}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
          <View style={styles.descriptionHelper}>
            <Text style={[
              styles.helperText,
              formData.description.length < 10 ? styles.helperTextWarning : styles.helperTextSuccess
            ]}>
              {formData.description.length}/1000 caractères (minimum 10)
            </Text>
          </View>
          {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
        </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Description détaillée *</Text>
        <TextInput
          style={[styles.textArea, errors.description && styles.inputError]}
          value={formData.description}
          onChangeText={(text) => updateFormData('description', text)}
          placeholder="Décrivez votre problème, vos besoins, les détails importants..."
          placeholderTextColor={COLORS.gray}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
        <View style={styles.descriptionHelper}>
          <Text style={[
            styles.helperText,
            formData.description.length < 10 ? styles.helperTextWarning : styles.helperTextSuccess
          ]}>
            {formData.description.length}/1000 caractères (minimum 10)
          </Text>
        </View>
        {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Budget estimé (€) *</Text>
        <TextInput
          style={[styles.textInput, errors.budget && styles.inputError]}
          value={formData.budget}
          onChangeText={(text) => updateFormData('budget', text.replace(/[^0-9.]/g, ''))}
          placeholder="150"
          placeholderTextColor={COLORS.gray}
          keyboardType="numeric"
        />
        {errors.budget && <Text style={styles.errorText}>{errors.budget}</Text>}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Date souhaitée *</Text>
        <View style={styles.dateSelector}>
          {[
            { key: '2024-12-15', label: 'Cette semaine' },
            { key: '2024-12-22', label: 'Semaine prochaine' },
            { key: '2024-12-29', label: 'Dans 2 semaines' },
            { key: '2025-01-05', label: 'Dans 3 semaines' }
          ].map((dateOption) => (
            <TouchableOpacity
              key={dateOption.key}
              style={[
                styles.dateOption,
                formData.preferredDate === dateOption.key && styles.dateOptionSelected
              ]}
              onPress={() => updateFormData('preferredDate', dateOption.key)}
            >
              <Text style={[
                styles.dateOptionText,
                formData.preferredDate === dateOption.key && styles.dateOptionTextSelected
              ]}>
                {dateOption.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {errors.preferredDate && <Text style={styles.errorText}>{errors.preferredDate}</Text>}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Adresse du service *</Text>
        <TextInput
          style={[styles.textInput, errors.location && styles.inputError]}
          value={formData.location}
          onChangeText={(text) => updateFormData('location', text)}
          placeholder="Adresse complète où le service doit être effectué"
          placeholderTextColor={COLORS.gray}
        />
        {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Informations supplémentaires</Text>
        <TextInput
          style={styles.textArea}
          value={formData.additionalInfo}
          onChangeText={(text) => updateFormData('additionalInfo', text)}
          placeholder="Précisions, contraintes, accès, codes d'entrée..."
          placeholderTextColor={COLORS.gray}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Téléphone de contact *</Text>
        <TextInput
          style={[styles.textInput, errors.contactPhone && styles.inputError]}
          value={formData.contactPhone}
          onChangeText={(text) => updateFormData('contactPhone', text)}
          placeholder="06 12 34 56 78"
          placeholderTextColor={COLORS.gray}
          keyboardType="phone-pad"
        />
        {errors.contactPhone && <Text style={styles.errorText}>{errors.contactPhone}</Text>}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Email de contact *</Text>
        <TextInput
          style={[styles.textInput, errors.contactEmail && styles.inputError]}
          value={formData.contactEmail}
          onChangeText={(text) => updateFormData('contactEmail', text)}
          placeholder="<EMAIL>"
          placeholderTextColor={COLORS.gray}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        {errors.contactEmail && <Text style={styles.errorText}>{errors.contactEmail}</Text>}
      </View>
    </View>
  );

  const renderSubmitButton = () => (
    <View style={styles.submitSection}>
      <Button
        title={isSubmitting ? 'Envoi en cours...' : 'Envoyer la demande'}
        onPress={handleSubmit}
        disabled={isSubmitting}
        style={styles.submitButton}
      />
      <Text style={styles.submitNote}>
        * Champs obligatoires
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderCategorySelector()}
        {renderFormFields()}
        {renderUrgencySelector()}
        {renderSubmitButton()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SIZES.padding,
    backgroundColor: COLORS.secondary,
  },
  backButton: {
    padding: SIZES.base,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.white,
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SIZES.extraLarge * 2,
  },
  section: {
    padding: SIZES.padding,
    backgroundColor: COLORS.white,
    marginBottom: SIZES.medium,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: SIZES.medium,
  },
  sectionSubtitle: {
    fontSize: SIZES.font,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
    marginBottom: SIZES.medium,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: (width - SIZES.padding * 4) / 2,
    backgroundColor: COLORS.light,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    alignItems: 'center',
    marginBottom: SIZES.medium,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  categoryCardSelected: {
    borderColor: COLORS.secondary,
    backgroundColor: COLORS.secondary + '10',
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.base,
  },
  categoryText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    textAlign: 'center',
  },
  categoryTextSelected: {
    color: COLORS.secondary,
    fontFamily: FONTS.bold,
  },
  urgencyGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  urgencyCard: {
    flex: 1,
    backgroundColor: COLORS.light,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    alignItems: 'center',
    marginHorizontal: SIZES.base / 2,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  urgencyCardSelected: {
    borderColor: COLORS.secondary,
    backgroundColor: COLORS.secondary + '10',
  },
  urgencyIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.base,
  },
  urgencyText: {
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    textAlign: 'center',
  },
  urgencyTextSelected: {
    color: COLORS.secondary,
    fontFamily: FONTS.bold,
  },
  inputGroup: {
    marginBottom: SIZES.medium,
  },
  inputLabel: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: SIZES.base,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    fontSize: SIZES.font,
    fontFamily: FONTS.regular,
    color: COLORS.dark,
    backgroundColor: COLORS.white,
  },
  textArea: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    fontSize: SIZES.font,
    fontFamily: FONTS.regular,
    color: COLORS.dark,
    backgroundColor: COLORS.white,
    minHeight: 100,
  },
  inputError: {
    borderColor: COLORS.danger,
  },
  errorText: {
    color: COLORS.danger,
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    marginTop: SIZES.base,
  },
  submitSection: {
    padding: SIZES.padding,
    backgroundColor: COLORS.white,
    alignItems: 'center',
  },
  submitButton: {
    width: '100%',
    marginBottom: SIZES.medium,
  },
  submitNote: {
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
    textAlign: 'center',
  },
  dateSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dateOption: {
    flex: 1,
    minWidth: (width - SIZES.padding * 4) / 2,
    backgroundColor: COLORS.light,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    alignItems: 'center',
    marginBottom: SIZES.medium,
    marginHorizontal: SIZES.base / 2,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  dateOptionSelected: {
    borderColor: COLORS.secondary,
    backgroundColor: COLORS.secondary + '10',
  },
  dateOptionText: {
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    textAlign: 'center',
  },
  dateOptionTextSelected: {
    color: COLORS.secondary,
    fontFamily: FONTS.bold,
  },
  descriptionHelper: {
    marginTop: SIZES.base,
    alignItems: 'flex-end',
  },
  helperText: {
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
  },
  helperTextWarning: {
    color: COLORS.warning,
  },
  helperTextSuccess: {
    color: COLORS.success,
  },
});

export default CreateServiceRequestScreen;
