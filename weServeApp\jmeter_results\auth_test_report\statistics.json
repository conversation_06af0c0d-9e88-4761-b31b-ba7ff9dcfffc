{"Test API Docs": {"transaction": "Test API Docs", "sampleCount": 1, "errorCount": 1, "errorPct": 100.0, "meanResTime": 0.0, "medianResTime": 0.0, "minResTime": 0.0, "maxResTime": 0.0, "pct1ResTime": 0.0, "pct2ResTime": 0.0, "pct3ResTime": 0.0, "throughput": "Infinity", "receivedKBytesPerSec": "Infinity", "sentKBytesPerSec": "NaN"}, "Test Endpoint Protégé (sans token)": {"transaction": "Test Endpoint Protégé (sans token)", "sampleCount": 1, "errorCount": 1, "errorPct": 100.0, "meanResTime": 0.0, "medianResTime": 0.0, "minResTime": 0.0, "maxResTime": 0.0, "pct1ResTime": 0.0, "pct2ResTime": 0.0, "pct3ResTime": 0.0, "throughput": "Infinity", "receivedKBytesPerSec": "Infinity", "sentKBytesPerSec": "NaN"}, "Total": {"transaction": "Total", "sampleCount": 4, "errorCount": 4, "errorPct": 100.0, "meanResTime": 11.25, "medianResTime": 1.0, "minResTime": 0.0, "maxResTime": 43.0, "pct1ResTime": 43.0, "pct2ResTime": 43.0, "pct3ResTime": 43.0, "throughput": 80.0, "receivedKBytesPerSec": 73.10546875, "sentKBytesPerSec": 2.3046875}, "Test Endpoint Public": {"transaction": "Test Endpoint Public", "sampleCount": 1, "errorCount": 1, "errorPct": 100.0, "meanResTime": 2.0, "medianResTime": 2.0, "minResTime": 2.0, "maxResTime": 2.0, "pct1ResTime": 2.0, "pct2ResTime": 2.0, "pct3ResTime": 2.0, "throughput": 500.0, "receivedKBytesPerSec": 552.24609375, "sentKBytesPerSec": 0.0}, "HTTP Request Defaults": {"transaction": "HTTP Request Defaults", "sampleCount": 1, "errorCount": 1, "errorPct": 100.0, "meanResTime": 43.0, "medianResTime": 43.0, "minResTime": 43.0, "maxResTime": 43.0, "pct1ResTime": 43.0, "pct2ResTime": 43.0, "pct3ResTime": 43.0, "throughput": 23.25581395348837, "receivedKBytesPerSec": 7.789789244186047, "sentKBytesPerSec": 2.679869186046512}}