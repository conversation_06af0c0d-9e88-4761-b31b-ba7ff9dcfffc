import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Image
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';

import Button from '../common/Button';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { SERVICE_CATEGORIES } from '../../constants/categories';
import { useAuthStore } from '../../stores/authStore';
import servicesService from '../../services/servicesService';

const CreateProviderServiceScreen = ({ navigation }) => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  
  // État du formulaire
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    price: '',
    location: {
      address: '',
      latitude: '',
      longitude: ''
    },
    availability: {
      monday: { available: true, startTime: '08:00', endTime: '18:00' },
      tuesday: { available: true, startTime: '08:00', endTime: '18:00' },
      wednesday: { available: true, startTime: '08:00', endTime: '18:00' },
      thursday: { available: true, startTime: '08:00', endTime: '18:00' },
      friday: { available: true, startTime: '08:00', endTime: '18:00' },
      saturday: { available: false, startTime: '09:00', endTime: '17:00' },
      sunday: { available: false, startTime: '09:00', endTime: '17:00' }
    }
  });

  const [photos, setPhotos] = useState([]);
  const [errors, setErrors] = useState({});

  // Validation du formulaire
  const validateForm = () => {
    const newErrors = {};

    // Validation du titre
    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Le titre ne peut pas dépasser 100 caractères';
    }

    // Validation de la description
    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
    } else if (formData.description.length < 10) {
      newErrors.description = 'La description doit faire au moins 10 caractères';
    } else if (formData.description.length > 1000) {
      newErrors.description = 'La description ne peut pas dépasser 1000 caractères';
    } else if (!/^[a-zA-ZÀ-ÿ\s\d.,!?-]+$/.test(formData.description)) {
      newErrors.description = 'La description doit être compréhensible';
    }

    // Validation de la catégorie
    if (!formData.category) {
      newErrors.category = 'La catégorie est requise';
    }

    // Validation du prix
    if (!formData.price || isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Le prix doit être un nombre positif';
    }

    // Validation de l'adresse
    if (!formData.location.address.trim()) {
      newErrors.location = 'L\'adresse est requise';
    }

    // Validation des coordonnées GPS
    const latitude = parseFloat(formData.location.latitude);
    const longitude = parseFloat(formData.location.longitude);
    
    if (!formData.location.latitude || !formData.location.longitude) {
      newErrors.coordinates = 'Les coordonnées GPS sont requises';
    } else if (isNaN(latitude) || isNaN(longitude)) {
      newErrors.coordinates = 'Les coordonnées GPS doivent être des nombres valides';
    } else if (latitude < -90 || latitude > 90) {
      newErrors.coordinates = 'La latitude doit être entre -90 et 90';
    } else if (longitude < -180 || longitude > 180) {
      newErrors.coordinates = 'La longitude doit être entre -180 et 180';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Sélectionner des photos
  const pickImages = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        selectionLimit: 5,
        quality: 0.8,
      });

      if (!result.canceled && result.assets) {
        const newPhotos = result.assets.map(asset => ({
          uri: asset.uri,
          type: 'image/jpeg',
          name: `photo_${Date.now()}.jpg`
        }));
        
        if (photos.length + newPhotos.length > 5) {
          Alert.alert('Limite atteinte', 'Vous ne pouvez pas sélectionner plus de 5 photos');
          return;
        }
        
        setPhotos([...photos, ...newPhotos]);
      }
    } catch (error) {
      console.error('Erreur lors de la sélection des photos:', error);
      Alert.alert('Erreur', 'Impossible de sélectionner les photos');
    }
  };

  // Supprimer une photo
  const removePhoto = (index) => {
    setPhotos(photos.filter((_, i) => i !== index));
  };

  // Soumettre le formulaire
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Préparer les données selon le format attendu par le backend
      const serviceData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        price: Number(formData.price),
        location: {
          address: formData.location.address,
          latitude: parseFloat(formData.location.latitude),
          longitude: parseFloat(formData.location.longitude)
        },
        availability: Object.entries(formData.availability).map(([day, schedule]) => ({
          day,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          available: schedule.available
        }))
      };

      console.log('📤 Envoi des données:', serviceData);

      // Appeler le service avec les données et les photos
      const response = await servicesService.createProviderService(serviceData, photos);
      
      if (response.success) {
        Alert.alert(
          'Succès !',
          'Votre service a été créé avec succès',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Services')
            }
          ]
        );
      } else {
        Alert.alert('Erreur', response.message || 'Erreur lors de la création du service');
      }
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      Alert.alert('Erreur', 'Erreur lors de la création du service');
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour un champ du formulaire
  const updateField = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  // Mettre à jour la disponibilité
  const updateAvailability = (day, field, value) => {
    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: {
          ...prev.availability[day],
          [field]: value
        }
      }
    }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.dark} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Créer un Service</Text>
        </View>

        <View style={styles.form}>
          {/* Informations de base */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations du Service</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Titre *</Text>
              <TextInput
                style={[styles.input, errors.title && styles.inputError]}
                value={formData.title}
                onChangeText={(value) => updateField('title', value)}
                placeholder="Ex: Services de Plomberie"
                maxLength={100}
              />
              {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Description *</Text>
              <TextInput
                style={[styles.textArea, errors.description && styles.inputError]}
                value={formData.description}
                onChangeText={(value) => updateField('description', value)}
                placeholder="Décrivez votre service en détail..."
                multiline
                numberOfLines={4}
                maxLength={1000}
              />
              {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Catégorie *</Text>
              <View style={styles.categoryGrid}>
                {SERVICE_CATEGORIES.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.categoryButton,
                      formData.category === category.id && styles.categoryButtonActive
                    ]}
                    onPress={() => updateField('category', category.id)}
                  >
                    <Text style={[
                      styles.categoryButtonText,
                      formData.category === category.id && styles.categoryButtonTextActive
                    ]}>
                      {category.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              {errors.category && <Text style={styles.errorText}>{errors.category}</Text>}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Prix par heure (€) *</Text>
              <TextInput
                style={[styles.input, errors.price && styles.inputError]}
                value={formData.price}
                onChangeText={(value) => updateField('price', value)}
                placeholder="65"
                keyboardType="numeric"
              />
              {errors.price && <Text style={styles.errorText}>{errors.price}</Text>}
            </View>
          </View>

          {/* Localisation */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Localisation</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Adresse *</Text>
              <TextInput
                style={[styles.input, errors.location && styles.inputError]}
                value={formData.location.address}
                onChangeText={(value) => updateField('location.address', value)}
                placeholder="Ex: Zone de Paris et banlieue"
              />
              {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
            </View>

            <View style={styles.coordinatesRow}>
              <View style={[styles.inputGroup, styles.halfWidth]}>
                <Text style={styles.label}>Latitude *</Text>
                <TextInput
                  style={[styles.input, errors.coordinates && styles.inputError]}
                  value={formData.location.latitude}
                  onChangeText={(value) => updateField('location.latitude', value)}
                  placeholder="48.8566"
                  keyboardType="numeric"
                />
              </View>

              <View style={[styles.inputGroup, styles.halfWidth]}>
                <Text style={styles.label}>Longitude *</Text>
                <TextInput
                  style={[styles.input, errors.coordinates && styles.inputError]}
                  value={formData.location.longitude}
                  onChangeText={(value) => updateField('location.longitude', value)}
                  placeholder="2.3522"
                  keyboardType="numeric"
                />
              </View>
            </View>
            
            {errors.coordinates && <Text style={styles.errorText}>{errors.coordinates}</Text>}
          </View>

          {/* Disponibilité */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Disponibilité</Text>
            
            {Object.entries(formData.availability).map(([day, schedule]) => (
              <View key={day} style={styles.availabilityRow}>
                <View style={styles.dayToggle}>
                  <TouchableOpacity
                    style={[
                      styles.toggleButton,
                      schedule.available && styles.toggleButtonActive
                    ]}
                    onPress={() => updateAvailability(day, 'available', !schedule.available)}
                  >
                    <Text style={[
                      styles.toggleText,
                      schedule.available && styles.toggleTextActive
                    ]}>
                      {day.charAt(0).toUpperCase() + day.slice(1)}
                    </Text>
                  </TouchableOpacity>
                </View>
                
                {schedule.available && (
                  <View style={styles.timeInputs}>
                    <TextInput
                      style={styles.timeInput}
                      value={schedule.startTime}
                      onChangeText={(value) => updateAvailability(day, 'startTime', value)}
                      placeholder="08:00"
                    />
                    <Text style={styles.timeSeparator}>-</Text>
                    <TextInput
                      style={styles.timeInput}
                      value={schedule.endTime}
                      onChangeText={(value) => updateAvailability(day, 'endTime', value)}
                      placeholder="18:00"
                    />
                  </View>
                )}
              </View>
            ))}
          </View>

          {/* Photos */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Photos (max 5)</Text>
            
            <TouchableOpacity style={styles.photoButton} onPress={pickImages}>
              <Ionicons name="camera" size={24} color={COLORS.primary} />
              <Text style={styles.photoButtonText}>Ajouter des photos</Text>
            </TouchableOpacity>

            {photos.length > 0 && (
              <View style={styles.photosGrid}>
                {photos.map((photo, index) => (
                  <View key={index} style={styles.photoItem}>
                    <Image source={{ uri: photo.uri }} style={styles.photo} />
                    <TouchableOpacity
                      style={styles.removePhotoButton}
                      onPress={() => removePhoto(index)}
                    >
                      <Ionicons name="close-circle" size={24} color={COLORS.danger} />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Bouton de soumission */}
          <View style={styles.submitSection}>
            <Button
              title={loading ? "Création..." : "Créer le Service"}
              onPress={handleSubmit}
              disabled={loading}
              style={styles.submitButton}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SIZES.padding,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    marginRight: SIZES.medium,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
  },
  form: {
    padding: SIZES.padding,
  },
  section: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: SIZES.medium,
    ...SHADOWS.small,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: SIZES.medium,
  },
  inputGroup: {
    marginBottom: SIZES.medium,
  },
  coordinatesRow: {
    flexDirection: 'row',
    gap: SIZES.base,
  },
  halfWidth: {
    flex: 1,
  },
  label: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: SIZES.base,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    fontSize: SIZES.font,
    color: COLORS.dark,
    backgroundColor: COLORS.white,
  },
  textArea: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    fontSize: SIZES.font,
    color: COLORS.dark,
    backgroundColor: COLORS.white,
    height: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: COLORS.danger,
  },
  errorText: {
    color: COLORS.danger,
    fontSize: SIZES.small,
    marginTop: SIZES.base,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.base,
  },
  categoryButton: {
    paddingHorizontal: SIZES.medium,
    paddingVertical: SIZES.base,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.light,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  categoryButtonActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  categoryButtonText: {
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  categoryButtonTextActive: {
    color: COLORS.white,
  },
  availabilityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SIZES.base,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  dayToggle: {
    flex: 1,
  },
  toggleButton: {
    paddingVertical: SIZES.base,
    paddingHorizontal: SIZES.medium,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.light,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  toggleButtonActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  toggleText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  toggleTextActive: {
    color: COLORS.white,
  },
  timeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeInput: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    padding: SIZES.base,
    fontSize: SIZES.small,
    color: COLORS.dark,
    backgroundColor: COLORS.white,
    width: 70,
    textAlign: 'center',
  },
  timeSeparator: {
    marginHorizontal: SIZES.base,
    fontSize: SIZES.font,
    color: COLORS.gray,
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SIZES.medium,
    borderWidth: 2,
    borderColor: COLORS.primary,
    borderStyle: 'dashed',
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.light,
  },
  photoButtonText: {
    marginLeft: SIZES.base,
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.base,
    marginTop: SIZES.medium,
  },
  photoItem: {
    position: 'relative',
  },
  photo: {
    width: 80,
    height: 80,
    borderRadius: SIZES.radius,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: COLORS.white,
    borderRadius: 12,
  },
  submitSection: {
    marginTop: SIZES.large,
    marginBottom: SIZES.large,
  },
  submitButton: {
    width: '100%',
  },
});

export default CreateProviderServiceScreen;
