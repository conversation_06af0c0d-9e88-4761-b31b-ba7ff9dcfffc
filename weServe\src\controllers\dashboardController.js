import { listUserRequests, listProviderCompleted } from '../services/serviceRequestService.js';

export async function myRequests(req, res, next) {
  try {
    const { status, from, to } = req.query;
    const list = await listUserRequests(req.user.id, { status, from, to });
    res.json(list);
  } catch (err) { next(err); }
}

export async function providerCompleted(req, res, next) {
  try {
    const { from, to } = req.query;
    const list = await listProviderCompleted(req.user.id, { from, to });
    res.json(list);
  } catch (err) { next(err); }
} 