@echo off
echo ========================================
echo    Test de JMeter pour weServeApp
echo ========================================
echo.

echo 🔍 Vérification de JMeter...
echo.

REM Essayer de trouver JMeter
set JMETER_FOUND=false

REM Vérifier dans le PATH
where jmeter >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ JMeter trouvé dans le PATH
    set JMETER_CMD=jmeter
    set JMETER_FOUND=true
    goto test_jmeter
)

REM Emplacements courants
if exist "C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files
    set JMETER_FOUND=true
    goto test_jmeter
)

if exist "C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files (x86)
    set JMETER_FOUND=true
    goto test_jmeter
)

if exist "%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Downloads
    set JMETER_FOUND=true
    goto test_jmeter
)

if exist "%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé sur le Bureau
    set JMETER_FOUND=true
    goto test_jmeter
)

if exist "C:\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans C:\
    set JMETER_FOUND=true
    goto test_jmeter
)

if "%JMETER_FOUND%"=="false" (
    echo ❌ JMeter non trouvé!
    echo.
    echo Veuillez:
    echo 1. Installer JMeter depuis: https://jmeter.apache.org/download_jmeter.cgi
    echo 2. Ou spécifier le chemin manuellement
    echo.
    set /p custom_path="Chemin complet vers jmeter.bat: "
    if exist "%custom_path%" (
        set JMETER_CMD="%custom_path%"
        echo ✅ Chemin personnalisé accepté
        set JMETER_FOUND=true
        goto test_jmeter
    ) else (
        echo ❌ Fichier non trouvé
        pause
        exit /b 1
    )
)

:test_jmeter
echo.
echo 🎯 Test de JMeter avec: %JMETER_CMD%
echo.

echo 📋 Vérification de la version...
%JMETER_CMD% --version
if %errorlevel% equ 0 (
    echo ✅ JMeter fonctionne correctement!
    echo.
    echo 🚀 Vous pouvez maintenant utiliser:
    echo - run_jmeter_tests.bat (pour les tests complets)
    echo - run_jmeter_tests.ps1 (PowerShell)
    echo.
) else (
    echo ❌ Erreur lors de l'exécution de JMeter
    echo Vérifiez l'installation et les permissions
)

echo.
echo Appuyez sur une touche pour continuer...
pause >nul 