import request from 'supertest';
import { app } from '../src/app.js';
import User from '../src/models/User.js';
import mongoose from 'mongoose';

describe('Profile Photo Management', () => {
  let testUser;
  let authToken;

  beforeAll(async () => {
    // Créer un utilisateur de test
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+1234567890',
      role: 'client'
    });

    // Simuler un token d'authentification (dans un vrai test, vous devriez utiliser un vrai JWT)
    authToken = 'test-token';
  });

  afterAll(async () => {
    // Nettoyer après les tests
    await User.findByIdAndDelete(testUser._id);
    await mongoose.connection.close();
  });

  describe('GET /api/v1/auth/profile', () => {
    it('should return user profile when authenticated', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.user).toHaveProperty('name', 'Test User');
      expect(response.body.user).toHaveProperty('email', '<EMAIL>');
    });

    it('should return 401 when not authenticated', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile');

      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/v1/auth/update-profile-photo', () => {
    it('should update profile photo when valid image is provided', async () => {
      // Créer un buffer d'image simulé
      const imageBuffer = Buffer.from('fake-image-data');
      
      const response = await request(app)
        .post('/api/v1/auth/update-profile-photo')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('photo', imageBuffer, 'test-image.jpg');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Photo de profil mise à jour avec succès');
      expect(response.body.user).toHaveProperty('avatarUrl');
    });

    it('should return 400 when no photo is provided', async () => {
      const response = await request(app)
        .post('/api/v1/auth/update-profile-photo')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Aucune photo n\'a été fournie');
    });
  });

  describe('DELETE /api/v1/auth/profile-photo', () => {
    it('should remove profile photo when authenticated', async () => {
      const response = await request(app)
        .delete('/api/v1/auth/profile-photo')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Photo de profil supprimée avec succès');
      expect(response.body.user.avatarUrl).toBeUndefined();
    });

    it('should return 401 when not authenticated', async () => {
      const response = await request(app)
        .delete('/api/v1/auth/profile-photo');

      expect(response.status).toBe(401);
    });
  });
});
