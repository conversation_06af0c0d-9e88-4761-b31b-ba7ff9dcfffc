import { API_BASE_URL, API_TIMEOUT, logApiCall, logApiResponse } from '../config/apiConfig';

// Classe principale pour gérer les appels API
class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.timeout = API_TIMEOUT;
  }

  // Méthode générique pour les requêtes HTTP
  async request(endpoint, options = {}) {
    // Récupérer le token depuis le store d'authentification
    let token = null;
    try {
      // Import dynamique pour éviter les problèmes de dépendances circulaires
      const { useAuthStore } = require('../stores/authStore');
      token = useAuthStore.getState().token;
    } catch (error) {
      console.warn('Impossible de récupérer le token:', error);
    }
    
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      // Inclure les cookies HttpOnly pour l'authentification
      credentials: 'include',
      ...options,
    };

    // Gestion du timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      clearTimeout(timeoutId);

      // Gestion des erreurs HTTP
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          response.status,
          errorData.message || `Erreur HTTP ${response.status}`,
          errorData
        );
      }

      // Parsing de la réponse
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return await response.text();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new ApiError(408, 'Délai d\'attente dépassé', {});
      }
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(0, error.message || 'Erreur de connexion', {});
    }
  }

  // Méthodes GET, POST, PUT, DELETE
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    logApiCall('GET', url, params);
    const result = await this.request(url, { method: 'GET' });
    logApiResponse(url, result);
    return result;
  }

  async post(endpoint, data = {}) {
    logApiCall('POST', endpoint, data);
    const result = await this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    logApiResponse(endpoint, result);
    return result;
  }

  async put(endpoint, data = {}) {
    logApiCall('PUT', endpoint, data);
    const result = await this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    logApiResponse(endpoint, result);
    return result;
  }

  async delete(endpoint) {
    logApiCall('DELETE', endpoint);
    const result = await this.request(endpoint, { method: 'DELETE' });
    logApiResponse(endpoint, result);
    return result;
  }

  // Upload de fichiers avec FormData
  async upload(endpoint, formData) {
    // Récupérer le token depuis le store d'authentification
    let token = null;
    try {
      const { useAuthStore } = require('../stores/authStore');
      token = useAuthStore.getState().token;
    } catch (error) {
      console.warn('Impossible de récupérer le token:', error);
    }
    
    return this.request(endpoint, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Pas de Content-Type pour FormData
      },
      body: formData,
    });
  }
}

// Classe d'erreur personnalisée
class ApiError extends Error {
  constructor(status, message, data) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Instance unique du service API
const apiService = new ApiService();

export default apiService;
export { ApiError };
