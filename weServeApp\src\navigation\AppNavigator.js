import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

import { useAuthStore } from '../stores/authStore';
import { getCompleteSafeConfig } from '../config/finalConfig';

// Import des écrans
import LoginScreen from '../components/screens/LoginScreen';
import SignupScreen from '../components/screens/SignupScreen';
import HomeScreen from '../components/screens/HomeScreen';
import ProfileScreen from '../components/screens/ProfileScreen';
import ServicesListScreen from '../components/screens/ServicesListScreen';
import DashboardScreen from '../components/screens/DashboardScreen';
import ServiceDetailScreen from '../components/screens/ServiceDetailScreen';
import CreateServiceRequestScreen from '../components/screens/CreateServiceRequestScreen';
import CreateProviderServiceScreen from '../components/screens/CreateProviderServiceScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Navigation par onglets pour les utilisateurs authentifiés
function MainTabs() {
  const { userRole } = useAuthStore();
  const safeConfig = getCompleteSafeConfig();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        ...safeConfig.navigation.tabs.screenOptions,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Services') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Create') {
            iconName = focused ? 'add-circle' : 'add-circle-outline';
          } else if (route.name === 'Dashboard') {
            iconName = focused ? 'stats-chart' : 'stats-chart-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarLabel: route.name === 'Create' 
          ? (userRole === 'client' ? 'Demander' : 'Créer')
          : route.name === 'Home' ? 'Accueil'
          : route.name === 'Services' ? 'Services'
          : route.name === 'Dashboard' ? 'Tableau'
          : 'Profil',
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Services" component={ServicesListScreen} />
      <Tab.Screen 
        name="Create" 
        component={userRole === 'client' ? CreateServiceRequestScreen : CreateProviderServiceScreen}
      />
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

// Navigation principale de l'application
export default function AppNavigator() {
  const { isAuthenticated } = useAuthStore();
  const safeConfig = getCompleteSafeConfig();

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={safeConfig.screenOptions}
        initialRouteName={isAuthenticated ? 'Main' : 'Login'}
      >
        {!isAuthenticated ? (
          // Écrans d'authentification
          <>
            <Stack.Screen 
              name="Login" 
              component={LoginScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen 
              name="Signup" 
              component={SignupScreen}
              options={{ headerShown: false }}
            />
          </>
        ) : (
          // Écrans principaux
          <>
            <Stack.Screen 
              name="Main" 
              component={MainTabs}
              options={{ headerShown: false }}
            />
            <Stack.Screen 
              name="ServiceDetail" 
              component={ServiceDetailScreen}
              options={{ 
                headerShown: true,
                title: 'Détails du service',
                headerBackTitle: 'Retour'
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
