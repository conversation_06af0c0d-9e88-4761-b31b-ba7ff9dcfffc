import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import Card from '../common/Card';
import { COLORS, SIZES, FONTS, SHADOWS } from '../../constants/theme';
import { useAuthStore } from '../../stores/authStore';
import { useServicesStore } from '../../stores/servicesStore';
import servicesService from '../../services/servicesService';

const { width } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  
  const { user, userRole } = useAuthStore();
  const { services, serviceRequests, isLoading } = useServicesStore();

  useEffect(() => {
    loadData();
  }, [selectedPeriod]);

  const loadData = async () => {
    try {
      if (userRole === 'client') {
        // Pour les clients : charger leurs demandes de service
        const result = await servicesService.getMyServiceRequests();
        if (result.success) {
          console.log('✅ Demandes chargées pour le tableau de bord client');
        }
      } else {
        // Pour les prestataires : charger leurs services
        const result = await servicesService.getMyProviderServices();
        if (result.success) {
          console.log('✅ Services chargés pour le tableau de bord prestataire');
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const renderHeader = () => (
    <LinearGradient
      colors={[COLORS.primary, COLORS.secondary]}
      style={styles.header}
    >
      <View style={styles.headerContent}>
        <View>
          <Text style={styles.headerTitle}>Tableau de bord</Text>
          <Text style={styles.headerSubtitle}>
            {userRole === 'client' 
              ? 'Vue d\'ensemble de vos demandes'
              : 'Vue d\'ensemble de vos services'
            }
          </Text>
        </View>
        <View style={styles.headerIcon}>
          <Ionicons 
            name="stats-chart" 
            size={40} 
            color={COLORS.white} 
          />
        </View>
      </View>
    </LinearGradient>
  );

  const renderPeriodSelector = () => (
    <View style={styles.periodContainer}>
      <Text style={styles.periodLabel}>Période :</Text>
      <View style={styles.periodButtons}>
        {[
          { key: 'week', label: 'Semaine' },
          { key: 'month', label: 'Mois' },
          { key: 'year', label: 'Année' }
        ].map((period) => (
          <TouchableOpacity
            key={period.key}
            style={[
              styles.periodButton,
              selectedPeriod === period.key && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period.key)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period.key && styles.periodButtonTextActive
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStatsCards = () => {
    if (userRole === 'client') {
      return (
        <View style={styles.statsGrid}>
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.primary, COLORS.accent]}
              style={styles.statIcon}
            >
              <Ionicons name="document-text" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>12</Text>
            <Text style={styles.statLabel}>Demandes totales</Text>
          </Card>
          
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.success, COLORS.secondary]}
              style={styles.statIcon}
            >
              <Ionicons name="checkmark-circle" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>8</Text>
            <Text style={styles.statLabel}>Terminées</Text>
          </Card>
          
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.warning, COLORS.accent]}
              style={styles.statIcon}
            >
              <Ionicons name="time" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>3</Text>
            <Text style={styles.statLabel}>En cours</Text>
          </Card>
          
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.gray, COLORS.dark]}
              style={styles.statIcon}
            >
              <Ionicons name="hourglass" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>1</Text>
            <Text style={styles.statLabel}>En attente</Text>
          </Card>
        </View>
      );
    } else {
      return (
        <View style={styles.statsGrid}>
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.primary, COLORS.accent]}
              style={styles.statIcon}
            >
              <Ionicons name="construct" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>24</Text>
            <Text style={styles.statLabel}>Services actifs</Text>
          </Card>
          
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.success, COLORS.secondary]}
              style={styles.statIcon}
            >
              <Ionicons name="star" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>4.8</Text>
            <Text style={styles.statLabel}>Note moyenne</Text>
          </Card>
          
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.warning, COLORS.accent]}
              style={styles.statIcon}
            >
              <Ionicons name="checkmark-circle" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>156</Text>
            <Text style={styles.statLabel}>Services effectués</Text>
          </Card>
          
          <Card style={styles.statCard}>
            <LinearGradient
              colors={[COLORS.gray, COLORS.dark]}
              style={styles.statIcon}
            >
              <Ionicons name="wallet" size={24} color={COLORS.white} />
            </LinearGradient>
            <Text style={styles.statValue}>2,450€</Text>
            <Text style={styles.statLabel}>Revenus du mois</Text>
          </Card>
        </View>
      );
    }
  };

  const renderRecentActivity = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Activité récente</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Services')}>
          <Text style={styles.seeAllText}>Voir tout</Text>
        </TouchableOpacity>
      </View>
      
      <Card style={styles.activityCard}>
        {userRole === 'client' ? (
          // Activité récente pour les clients
          <View>
            {[
              { title: 'Réparation plomberie', status: 'Terminé', date: 'Aujourd\'hui', icon: 'checkmark-circle', color: COLORS.success },
              { title: 'Installation électrique', status: 'En cours', date: 'Hier', icon: 'time', color: COLORS.warning },
              { title: 'Nettoyage ménage', status: 'En attente', date: 'Il y a 2 jours', icon: 'hourglass', color: COLORS.gray }
            ].map((activity, index) => (
              <View key={index} style={styles.activityItem}>
                <View style={[styles.activityIcon, { backgroundColor: activity.color + '20' }]}>
                  <Ionicons name={activity.icon} size={20} color={activity.color} />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityTitle}>{activity.title}</Text>
                  <Text style={styles.activityStatus}>{activity.status}</Text>
                </View>
                <Text style={styles.activityDate}>{activity.date}</Text>
              </View>
            ))}
          </View>
        ) : (
          // Activité récente pour les prestataires
          <View>
            {[
              { title: 'Plomberie - M. Dupont', status: 'Terminé', date: 'Aujourd\'hui', icon: 'checkmark-circle', color: COLORS.success },
              { title: 'Électricité - Mme Martin', status: 'En cours', date: 'Hier', icon: 'time', color: COLORS.warning },
              { title: 'Ménage - M. Bernard', status: 'Programmé', date: 'Demain', icon: 'calendar', color: COLORS.primary }
            ].map((activity, index) => (
              <View key={index} style={styles.activityItem}>
                <View style={[styles.activityIcon, { backgroundColor: activity.color + '20' }]}>
                  <Ionicons name={activity.icon} size={20} color={activity.color} />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityTitle}>{activity.title}</Text>
                  <Text style={styles.activityStatus}>{activity.status}</Text>
                </View>
                <Text style={styles.activityDate}>{activity.date}</Text>
              </View>
            ))}
          </View>
        )}
      </Card>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Actions rapides</Text>
      <View style={styles.quickActionsGrid}>
        <TouchableOpacity
          style={styles.quickActionCard}
          onPress={() => {
            if (userRole === 'client') {
              navigation.navigate('CreateServiceRequest');
            } else {
              navigation.navigate('CreateProviderService');
            }
          }}
        >
          <LinearGradient
            colors={[COLORS.primary, COLORS.accent]}
            style={styles.quickActionIcon}
          >
            <Ionicons 
              name={userRole === 'client' ? 'add-circle' : 'construct'} 
              size={24} 
              color={COLORS.white} 
            />
          </LinearGradient>
          <Text style={styles.quickActionText}>
            {userRole === 'client' ? 'Nouvelle demande' : 'Créer un service'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickActionCard}
          onPress={() => navigation.navigate('Services')}
        >
          <LinearGradient
            colors={[COLORS.secondary, COLORS.success]}
            style={styles.quickActionIcon}
          >
            <Ionicons name="list" size={24} color={COLORS.white} />
          </LinearGradient>
          <Text style={styles.quickActionText}>
            {userRole === 'client' ? 'Voir mes demandes' : 'Gérer mes services'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}
        {renderPeriodSelector()}
        {renderStatsCards()}
        {renderRecentActivity()}
        {renderQuickActions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SIZES.extraLarge,
  },
  header: {
    padding: SIZES.padding * 1.5,
    marginBottom: SIZES.extraLarge,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: FONTS.bold,
    color: COLORS.white,
    marginBottom: SIZES.base,
  },
  headerSubtitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.regular,
    color: COLORS.white,
    opacity: 0.9,
  },
  headerIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  periodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.extraLarge,
  },
  periodLabel: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginRight: SIZES.medium,
  },
  periodButtons: {
    flexDirection: 'row',
  },
  periodButton: {
    paddingHorizontal: SIZES.medium,
    paddingVertical: SIZES.base,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.white,
    marginRight: SIZES.base,
    ...SHADOWS.small,
  },
  periodButtonActive: {
    backgroundColor: COLORS.primary,
  },
  periodButtonText: {
    fontSize: SIZES.small,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
  },
  periodButtonTextActive: {
    color: COLORS.white,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.extraLarge,
  },
  statCard: {
    width: (width - SIZES.padding * 3) / 2,
    alignItems: 'center',
    padding: SIZES.medium,
    marginBottom: SIZES.medium,
  },
  statIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.medium,
  },
  statValue: {
    fontSize: SIZES.extraLarge,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
    marginBottom: SIZES.base,
  },
  statLabel: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.gray,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.extraLarge,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.medium,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontFamily: FONTS.bold,
    color: COLORS.dark,
  },
  seeAllText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  activityCard: {
    padding: 0,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SIZES.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SIZES.medium,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    marginBottom: SIZES.base,
  },
  activityStatus: {
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
  activityDate: {
    fontSize: SIZES.small,
    fontFamily: FONTS.regular,
    color: COLORS.gray,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: (width - SIZES.padding * 3) / 2,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.medium,
    alignItems: 'center',
    ...SHADOWS.small,
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.medium,
  },
  quickActionText: {
    fontSize: SIZES.font,
    fontFamily: FONTS.medium,
    color: COLORS.dark,
    textAlign: 'center',
  },
});

export default DashboardScreen;
