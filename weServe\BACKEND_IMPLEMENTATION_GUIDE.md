# 📋 WeServe Backend Implementation Guide

## 🎯 **Objectif**
Ce document détaille ce qui doit être implémenté dans le backend pour que l'application WeServe fonctionne complètement.

---

## 🚨 **PRIORITÉ MAXIMALE - Endpoints critiques manquants**

### 1. **Création de Service** - `POST /api/v1/services`
**Statut :** ❌ MANQUANT  
**Impact :** L'application ne peut pas créer de demandes de service

**Données reçues du frontend :**
```json
{
  "title": "string (max 100 chars)",
  "description": "string (max 1000 chars)", 
  "category": "string (enum: Plomberie, Électricité, Ménage, Jardinage, Déménagement, Peinture, Réparation, Installation, Autre)",
  "budget": "number (€)",
  "date": "ISO date string",
  "location": {
    "address": "string",
    "latitude": "number",
    "longitude": "number"
  },
  "photos": "File[] (max 5 photos, 10MB total)",
  "clientId": "string"
}
```

**Réponse attendue :**
```json
{
  "success": true,
  "data": {
    "id": "string",
    "title": "string",
    "status": "pending",
    "createdAt": "ISO date"
  }
}
```

---

## 🏗️ **Structure de Base de Données Requise**

### Table `users`
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  role ENUM('client', 'provider', 'admin') DEFAULT 'client',
  profile_picture VARCHAR(255),
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Table `services`
```sql
CREATE TABLE services (
  id SERIAL PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(50) NOT NULL,
  budget DECIMAL(10,2) NOT NULL,
  date DATE NOT NULL,
  address TEXT,
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  client_id INTEGER REFERENCES users(id) NOT NULL,
  status ENUM('pending', 'accepted', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
  urgency ENUM('low', 'medium', 'high') DEFAULT 'medium',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Table `service_photos`
```sql
CREATE TABLE service_photos (
  id SERIAL PRIMARY KEY,
  service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
  photo_url VARCHAR(500) NOT NULL,
  photo_path VARCHAR(500) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Table `service_proposals`
```sql
CREATE TABLE service_proposals (
  id SERIAL PRIMARY KEY,
  service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
  provider_id INTEGER REFERENCES users(id) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  description TEXT,
  estimated_duration VARCHAR(100),
  status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## 🔐 **Système d'Authentification Requis**

### Endpoints d'authentification
```
POST   /api/v1/auth/signup          # Inscription
POST   /api/v1/auth/login           # Connexion
POST   /api/v1/auth/refresh         # Rafraîchir le token
POST   /api/v1/auth/logout          # Déconnexion
GET    /api/v1/auth/me              # Profil utilisateur actuel
```

### Structure des données d'authentification
```json
// POST /api/v1/auth/signup
{
  "email": "string",
  "password": "string (min 8 chars)",
  "firstName": "string",
  "lastName": "string",
  "phone": "string (optionnel)",
  "role": "client" | "provider"
}

// POST /api/v1/auth/login
{
  "email": "string",
  "password": "string"
}

// Réponse d'authentification
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "string"
    },
    "tokens": {
      "accessToken": "string",
      "refreshToken": "string"
    }
  }
}
```

---

## 📱 **Endpoints de Services Requis**

### Gestion des services
```
GET    /api/v1/services              # Liste des services avec filtres
GET    /api/v1/services/:id          # Détail d'un service
POST   /api/v1/services              # Créer un service (CRITIQUE)
PUT    /api/v1/services/:id          # Modifier un service
DELETE /api/v1/services/:id          # Supprimer un service
PATCH  /api/v1/services/:id/status   # Changer le statut
```

### Gestion des photos
```
POST   /api/v1/services/:id/photos   # Ajouter des photos
DELETE /api/v1/services/:id/photos/:photoId  # Supprimer une photo
```

### Recherche et filtres
```
GET    /api/v1/services/search       # Recherche par mot-clé
GET    /api/v1/services/categories   # Liste des catégories
GET    /api/v1/services/nearby       # Services à proximité
```

---

## 🔧 **Configuration Technique Requise**

### 1. **Gestion des fichiers multipart**
```javascript
// Exemple avec Multer (Node.js)
const multer = require('multer');

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/services/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + '.jpg');
  }
});

const upload = multer({ 
  storage: storage,
  limits: { 
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5 // Max 5 photos
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});
```

### 2. **Middleware d'authentification**
```javascript
const authenticateToken = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: 'Access denied. No token provided.' 
    });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ 
      success: false, 
      message: 'Invalid token.' 
    });
  }
};
```

### 3. **Validation des données**
```javascript
const validateServiceData = (data) => {
  const errors = [];
  
  if (!data.title || data.title.trim().length === 0) {
    errors.push('Title is required');
  }
  if (data.title && data.title.length > 100) {
    errors.push('Title must be less than 100 characters');
  }
  
  if (!data.description || data.description.trim().length === 0) {
    errors.push('Description is required');
  }
  if (data.description && data.description.length > 1000) {
    errors.push('Description must be less than 1000 characters');
  }
  
  if (!data.category) {
    errors.push('Category is required');
  }
  
  if (!data.budget || isNaN(data.budget) || data.budget <= 0) {
    errors.push('Valid budget is required');
  }
  
  if (!data.date) {
    errors.push('Date is required');
  }
  
  if (!data.location || !data.location.address) {
    errors.push('Location is required');
  }
  
  return errors;
};
```

---

## 🌐 **Configuration Serveur Requise**

### 1. **CORS pour l'application mobile**
```javascript
const cors = require('cors');

app.use(cors({
  origin: [
    'http://localhost:3000',
    'exp://localhost:8081',
    'exp://*************:8081' // Adresse IP de votre appareil
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
```

### 2. **Variables d'environnement**
```bash
# .env
PORT=4000
NODE_ENV=development
JWT_SECRET=your_super_secret_jwt_key_here
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here
DATABASE_URL=postgresql://username:password@localhost:5432/weserve_db
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
```

### 3. **Gestion des erreurs**
```javascript
// Middleware de gestion d'erreurs global
app.use((error, req, res, next) => {
  console.error('Error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.errors
    });
  }
  
  if (error.name === 'MulterError') {
    return res.status(400).json({
      success: false,
      message: 'File upload error',
      error: error.message
    });
  }
  
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error'
  });
});
```

---

## 📋 **Checklist d'Implémentation**

### Phase 1 - Base (Semaine 1)
- [ ] Configuration du serveur Express/Node.js
- [ ] Configuration de la base de données PostgreSQL
- [ ] Configuration CORS
- [ ] Middleware d'authentification JWT
- [ ] Endpoints d'authentification (signup/login)

### Phase 2 - Services (Semaine 2)
- [ ] Table `services` dans la base de données
- [ ] Table `service_photos` dans la base de données
- [ ] Endpoint `POST /api/v1/services` avec gestion multipart
- [ ] Validation des données de service
- [ ] Upload et stockage des photos

### Phase 3 - Fonctionnalités (Semaine 3)
- [ ] Endpoints de récupération des services
- [ ] Système de recherche et filtres
- [ ] Gestion des catégories
- [ ] Endpoints de modification/suppression

### Phase 4 - Tests et Optimisation (Semaine 4)
- [ ] Tests unitaires des endpoints
- [ ] Tests d'intégration
- [ ] Optimisation des performances
- [ ] Documentation API

---

## 🧪 **Tests Requis**

### Tests d'endpoints
```javascript
// Exemple avec Jest
describe('POST /api/v1/services', () => {
  test('should create a service with valid data', async () => {
    const serviceData = {
      title: 'Test Service',
      description: 'Test Description',
      category: 'Réparation',
      budget: 100,
      date: '2024-12-15',
      location: {
        address: '123 Test Street',
        latitude: 48.8566,
        longitude: 2.3522
      }
    };
    
    const response = await request(app)
      .post('/api/v1/services')
      .set('Authorization', `Bearer ${validToken}`)
      .field('title', serviceData.title)
      .field('description', serviceData.description)
      .field('category', serviceData.category)
      .field('budget', serviceData.budget)
      .field('date', serviceData.date)
      .field('location[address]', serviceData.location.address)
      .field('location[latitude]', serviceData.location.latitude)
      .field('location[longitude]', serviceData.location.longitude)
      .attach('photos', 'test-image.jpg');
    
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data.title).toBe(serviceData.title);
  });
});
```

---

## 🚀 **Démarrage Rapide**

### 1. **Installer les dépendances**
```bash
npm init -y
npm install express cors multer jsonwebtoken bcryptjs pg dotenv
npm install --save-dev nodemon jest supertest
```

### 2. **Structure des dossiers**
```
backend/
├── src/
│   ├── controllers/
│   │   ├── authController.js
│   │   └── serviceController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── upload.js
│   │   └── validation.js
│   ├── models/
│   │   ├── userModel.js
│   │   └── serviceModel.js
│   ├── routes/
│   │   ├── authRoutes.js
│   │   └── serviceRoutes.js
│   ├── utils/
│   │   └── database.js
│   └── app.js
├── uploads/
├── .env
└── package.json
```

### 3. **Fichier principal app.js**
```javascript
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'exp://localhost:8081'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/v1/auth', require('./src/routes/authRoutes'));
app.use('/api/v1/services', require('./src/routes/serviceRoutes'));

// Error handling
app.use((error, req, res, next) => {
  console.error(error);
  res.status(500).json({ success: false, message: 'Internal server error' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

---

## 📞 **Support et Questions**

Pour toute question sur cette implémentation :
1. Vérifiez d'abord la checklist d'implémentation
2. Consultez les exemples de code fournis
3. Testez chaque endpoint avec Postman avant l'intégration mobile
4. Vérifiez les logs du serveur pour identifier les erreurs

---

**⚠️ IMPORTANT :** Commencez par l'endpoint `POST /api/v1/services` car c'est le plus critique pour le fonctionnement de l'application mobile.
