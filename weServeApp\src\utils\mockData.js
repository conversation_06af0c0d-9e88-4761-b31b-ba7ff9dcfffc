// Données mock pour les services des prestataires
export const mockServices = [
  {
    id: '1',
    title: 'Réparation plomberie',
    description: 'Plombier professionnel avec 15 ans d\'expérience. Réparation de fuites, installation de robinets, débouchage de canalisations.',
    category: 'Plomberie',
    price: 45,
    rating: 4.8,
    reviewCount: 127,
    location: 'Paris, 11e arrondissement',
    provider: {
      name: '<PERSON>',
      rating: 4.8,
      completedServices: 156
    }
  },
  {
    id: '2',
    title: 'Installation électrique',
    description: 'Électricien certifié pour installations résidentielles et commerciales. Mise aux normes, tableaux électriques, éclairage.',
    category: 'Électricité',
    price: 55,
    rating: 4.9,
    reviewCount: 89,
    location: 'Paris, 8e arrondissement',
    provider: {
      name: '<PERSON>',
      rating: 4.9,
      completedServices: 203
    }
  },
  {
    id: '3',
    title: 'Nettoyage ménager',
    description: 'Service de nettoyage professionnel à domicile. Ménage régulier, nettoyage après travaux, repassage inclus.',
    category: 'Ménage',
    price: 25,
    rating: 4.7,
    reviewCount: 234,
    location: 'Paris, 16e arrondissement',
    provider: {
      name: '<PERSON>',
      rating: 4.7,
      completedServices: 89
    }
  },
  {
    id: '4',
    title: 'Jardinage et entretien',
    description: 'Jardinier paysagiste pour création et entretien d\'espaces verts. Taille, plantation, tonte, arrosage automatique.',
    category: 'Jardinage',
    price: 35,
    rating: 4.6,
    reviewCount: 67,
    location: 'Paris, 12e arrondissement',
    provider: {
      name: 'Pierre Dubois',
      rating: 4.6,
      completedServices: 134
    }
  },
  {
    id: '5',
    title: 'Peinture intérieure',
    description: 'Peintre professionnel pour rénovation intérieure. Préparation des murs, peinture, papiers peints, finitions.',
    category: 'Peinture',
    price: 40,
    rating: 4.8,
    reviewCount: 156,
    location: 'Paris, 6e arrondissement',
    provider: {
      name: 'Claude Moreau',
      rating: 4.8,
      completedServices: 178
    }
  }
];

// Données mock pour les demandes de services des clients
export const mockServiceRequests = [
  {
    id: '1',
    title: 'Réparation fuite robinet cuisine',
    description: 'J\'ai une fuite au niveau du robinet de ma cuisine. Il goutte constamment et j\'ai besoin d\'une réparation rapide.',
    category: 'Plomberie',
    budget: 80,
    status: 'En attente',
    location: 'Paris, 3e arrondissement',
    clientId: '689c76d7e8987288b774f5bd', // ID du client connecté
    client: {
      name: 'Thomas Leroy',
      phone: '+33123456789'
    },
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    title: 'Installation prise électrique',
    description: 'Besoin d\'installer une nouvelle prise électrique dans ma chambre pour mon bureau. Installation simple.',
    category: 'Électricité',
    budget: 120,
    status: 'Accepté',
    location: 'Paris, 7e arrondissement',
    clientId: '689c76d7e8987288b774f5bd', // ID du client connecté
    client: {
      name: 'Julie Rousseau',
      phone: '+33123456790'
    },
    createdAt: '2024-01-14'
  },
  {
    id: '3',
    title: 'Nettoyage après déménagement',
    description: 'Appartement à nettoyer après déménagement. Surface de 60m², 2 chambres, cuisine et salle de bain.',
    category: 'Ménage',
    budget: 150,
    status: 'En cours',
    location: 'Paris, 15e arrondissement',
    clientId: '689c76d7e8987288b774f5bd', // ID du client connecté
    client: {
      name: 'Marc Durand',
      phone: '+33123456791'
    },
    createdAt: '2024-01-13'
  },
  {
    id: '4',
    title: 'Taille haie et arbustes',
    description: 'Jardin de 200m² avec haie de 15m et plusieurs arbustes à tailler. Travail à faire en février.',
    category: 'Jardinage',
    budget: 200,
    status: 'Terminé',
    location: 'Paris, 20e arrondissement',
    client: {
      name: 'Anne Petit',
      phone: '+33123456792'
    },
    createdAt: '2024-01-10'
  },
  {
    id: '5',
    title: 'Peinture salon et couloir',
    description: 'Refaire la peinture du salon (25m²) et du couloir (15m²). Couleur blanche, préparation des murs incluse.',
    category: 'Peinture',
    budget: 400,
    status: 'En attente',
    location: 'Paris, 9e arrondissement',
    client: {
      name: 'Paul Simon',
      phone: '+33123456793'
    },
    createdAt: '2024-01-12'
  }
];

// Données mock pour les utilisateurs
export const mockUsers = [
  {
    id: '1',
    firstName: 'Jean',
    lastName: 'Dupont',
    email: '<EMAIL>',
    phone: '+33123456789',
    role: 'provider',
    avatar: null,
    rating: 4.8,
    completedServices: 156,
    specialties: ['Plomberie', 'Chauffage'],
    location: 'Paris, 11e arrondissement'
  },
  {
    id: '2',
    firstName: 'Marie',
    lastName: 'Martin',
    email: '<EMAIL>',
    phone: '+33123456790',
    role: 'provider',
    avatar: null,
    rating: 4.9,
    completedServices: 203,
    specialties: ['Électricité', 'Domotique'],
    location: 'Paris, 8e arrondissement'
  },
  {
    id: '3',
    firstName: 'Thomas',
    lastName: 'Leroy',
    email: '<EMAIL>',
    phone: '+33123456791',
    role: 'client',
    avatar: null,
    completedRequests: 8,
    location: 'Paris, 3e arrondissement'
  }
];

// Fonction pour obtenir des données aléatoires
export const getRandomService = () => {
  const randomIndex = Math.floor(Math.random() * mockServices.length);
  return mockServices[randomIndex];
};

export const getRandomServiceRequest = () => {
  const randomIndex = Math.floor(Math.random() * mockServiceRequests.length);
  return mockServiceRequests[randomIndex];
};

export const getRandomUser = () => {
  const randomIndex = Math.floor(Math.random() * mockUsers.length);
  return mockUsers[randomIndex];
};
