import express from 'express';
import { authenticate } from '../middlewares/auth.js';
import { validate } from '../middlewares/validate.js';
import { serviceRequestSchema, updateStatusSchema } from '../utils/joiSchemas.js';
import { 
  create, 
  listOpen, 
  getById, 
  changeStatus,
  searchServices,
  getCategories,
  getNearbyServices,
  getMyServices
} from '../controllers/serviceRequestController.js';

const router = express.Router();

// Créer une nouvelle demande de service (client uniquement)
router.post('/', 
  authenticate, 
  validate(serviceRequestSchema), 
  create
);

// Lister les demandes de service ouvertes (prestataires)
router.get('/', 
  authenticate, 
  listOpen
);

// Obtenir une demande de service spécifique
router.get('/:id', 
  authenticate, 
  getById
);

// Mettre à jour le statut d'une demande
router.patch('/:id/status', 
  authenticate, 
  validate(updateStatusSchema), 
  changeStatus
);

// Rechercher des services par critères
router.get('/search', 
  authenticate, 
  searchServices
);

// Obtenir les catégories disponibles
router.get('/categories', 
  authenticate, 
  getCategories
);

// Obtenir les services à proximité
router.get('/nearby', 
  authenticate, 
  getNearbyServices
);

// Obtenir les services de l'utilisateur connecté
router.get('/my-services', 
  authenticate, 
  getMyServices
);

export default router; 