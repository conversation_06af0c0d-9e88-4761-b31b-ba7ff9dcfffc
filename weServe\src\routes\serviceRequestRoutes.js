import express from 'express';
import { authenticate } from '../middlewares/auth.js';
import { validate } from '../middlewares/validate.js';
import { serviceRequestSchema, acceptRequestSchema, chooseProviderSchema, updateStatusSchema } from '../utils/joiSchemas.js';
import { 
  create, 
  acceptServiceRequest, 
  chooseProvider,
  getServiceRequest,
  listServiceRequests,
  updateServiceRequestStatus
} from '../controllers/serviceRequestController.js';

const router = express.Router();

// Créer une nouvelle demande de service (client uniquement)
router.post('/', 
  authenticate, 
  validate(serviceRequestSchema), 
  create
);

// Obtenir une demande de service spécifique
router.get('/:id', 
  authenticate, 
  getServiceRequest
);

// Lister les demandes de service (avec filtres)
router.get('/', 
  authenticate, 
  listServiceRequests
);

// Accepter une demande de service (prestataire uniquement)
router.post('/:id/accept', 
  authenticate, 
  validate(acceptRequestSchema), 
  acceptServiceRequest
);

// Choisir un prestataire (client uniquement)
router.post('/:id/choose', 
  authenticate, 
  validate(chooseProviderSchema), 
  chooseProvider
);

// Mettre à jour le statut d'une demande
router.patch('/:id/status', 
  authenticate, 
  validate(updateStatusSchema), 
  updateServiceRequestStatus
);

export default router; 