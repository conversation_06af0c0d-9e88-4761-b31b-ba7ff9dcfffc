{"name": "weserve-backend", "version": "1.0.0", "description": "WeServe platform backend (Node.js, Express, MongoDB, JWT, Passport, Multer, Cloudinary)", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --runInBand", "test:watch": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --watchAll", "lint": "eslint .", "docs": "node src/config/swagger.js"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@twilio/conversations": "^2.6.3", "bcryptjs": "^2.4.3", "body-parser": "^2.2.0", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "joi": "^17.12.3", "jsonwebtoken": "^9.0.2", "mongodb": "^6.18.0", "mongoose": "^8.5.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.8.0"}, "devDependencies": {"eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^7.0.0"}, "jest": {"testEnvironment": "node", "roots": ["<rootDir>/tests"], "moduleFileExtensions": ["js", "json"], "verbose": true, "collectCoverage": true, "collectCoverageFrom": ["src/services/**/*.js"]}}