import Joi from 'joi';

export const signupSchema = Joi.object({
  name: Joi.string().min(2).required(),
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  password: Joi.string().min(6).optional(),
  role: Joi.string().valid('client', 'provider').default('client')
}).custom((value, helpers) => {
  if (!value.email && !value.phone) return helpers.error('any.custom', { message: 'email or phone required' });
  if (value.email && !value.password) return helpers.error('any.custom', { message: 'password required for email signup' });
  return value;
}, 'email/phone requirement');

export const loginSchema = Joi.object({
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  password: Joi.string().min(6).optional()
}).custom((value, helpers) => {
  if (!value.email && !value.phone) return helpers.error('any.custom', { message: 'email or phone required' });
  if (value.email && !value.password) return helpers.error('any.custom', { message: 'password required for email login' });
  return value;
}, 'email/phone requirement');

// Schéma pour la création de demande de service
export const serviceRequestSchema = Joi.object({
  title: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Le titre doit contenir au moins 3 caractères',
    'string.max': 'Le titre ne peut pas dépasser 100 caractères',
    'any.required': 'Le titre est requis'
  }),
  description: Joi.string().min(10).max(1000).required().messages({
    'string.min': 'La description doit contenir au moins 10 caractères',
    'string.max': 'La description ne peut pas dépasser 1000 caractères',
    'any.required': 'La description est requise'
  }),
  category: Joi.string().valid('Plomberie', 'Électricité', 'Ménage', 'Jardinage', 'Déménagement', 'Peinture', 'Réparation', 'Installation', 'Autre').required().messages({
    'any.only': 'Catégorie invalide',
    'any.required': 'La catégorie est requise'
  }),
  budget: Joi.number().min(0).required().messages({
    'number.min': 'Le budget doit être positif',
    'any.required': 'Le budget est requis'
  }),
  date: Joi.date().greater('now').required().messages({
    'date.greater': 'La date doit être dans le futur',
    'any.required': 'La date est requise'
  }),
  location: Joi.object({
    address: Joi.string().required().messages({
      'any.required': 'L\'adresse est requise'
    }),
    longitude: Joi.number().min(-180).max(180).required().messages({
      'number.min': 'Longitude invalide',
      'number.max': 'Longitude invalide',
      'any.required': 'La longitude est requise'
    }),
    latitude: Joi.number().min(-90).max(90).required().messages({
      'number.min': 'Latitude invalide',
      'number.max': 'Latitude invalide',
      'any.required': 'La latitude est requise'
    })
  }).required().messages({
    'any.required': 'La localisation est requise'
  }),
  photos: Joi.array().items(Joi.string()).max(5).optional().messages({
    'array.max': 'Maximum 5 photos autorisées'
  })
});

// Schéma pour mettre à jour le statut
export const updateStatusSchema = Joi.object({
  status: Joi.string().valid('pending', 'accepted', 'in_progress', 'completed', 'cancelled').required().messages({
    'any.only': 'Statut invalide',
    'any.required': 'Le statut est requis'
  }),
  reason: Joi.string().max(500).optional().messages({
    'string.max': 'La raison ne peut pas dépasser 500 caractères'
  })
});

// Schéma pour accepter une demande
export const acceptRequestSchema = Joi.object({
  message: Joi.string().max(500).optional().messages({
    'string.max': 'Le message ne peut pas dépasser 500 caractères'
  })
});

// Schéma pour choisir un prestataire
export const chooseProviderSchema = Joi.object({
  providerId: Joi.string().required().messages({
    'any.required': 'L\'ID du prestataire est requis'
  })
});

// Validation pour les services des prestataires
export const validateProviderServiceData = (data, isUpdate = false) => {
  const availabilitySchema = Joi.object({
    day: Joi.string().valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday').required(),
    startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(), // Format HH:MM
    endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(), // Format HH:MM
    available: Joi.boolean().default(true)
  });

  const schema = Joi.object({
    title: isUpdate ? Joi.string().min(3).max(100) : Joi.string().min(3).max(100).required(),
    description: isUpdate ? Joi.string().min(10).max(1000) : Joi.string().min(10).max(1000).required(),
    category: isUpdate ? Joi.string().valid('Plomberie', 'Électricité', 'Ménage', 'Jardinage', 'Déménagement', 'Peinture', 'Réparation', 'Installation', 'Autre') : Joi.string().valid('Plomberie', 'Électricité', 'Ménage', 'Jardinage', 'Déménagement', 'Peinture', 'Réparation', 'Installation', 'Autre').required(),
    price: isUpdate ? Joi.number().min(0).max(1000) : Joi.number().min(0).max(1000).required(),
    location: isUpdate ? Joi.object({
      address: Joi.string().required(),
      latitude: Joi.number().required(),
      longitude: Joi.number().required()
    }) : Joi.object({
      address: Joi.string().required(),
      latitude: Joi.number().required(),
      longitude: Joi.number().required()
    }).required(),
    availability: isUpdate ? Joi.array().items(availabilitySchema) : Joi.array().items(availabilitySchema).min(1).required(),
    status: isUpdate ? Joi.string().valid('active', 'inactive', 'suspended') : Joi.string().valid('active', 'inactive', 'suspended').default('active')
  });

  return schema.validate(data);
};

export const reviewCreateSchema = Joi.object({
  serviceRequestId: Joi.string().required(),
  providerId: Joi.string().required(),
  rating: Joi.number().integer().min(1).max(5).required(),
  comment: Joi.string().allow('').optional()
});

export const refreshSchema = Joi.object({
  refreshToken: Joi.string().required()
});

export const requestResetSchema = Joi.object({
  email: Joi.string().email().optional(),
  phone: Joi.string().optional()
}).custom((value, helpers) => {
  if (!value.email && !value.phone) return helpers.error('any.custom', { message: 'email or phone required' });
  return value;
});

export const resetPasswordSchema = Joi.object({
  token: Joi.string().required(),
  newPassword: Joi.string().min(6).required()
});

// Validation pour le chat
export const chatMessageSchema = Joi.object({
  content: Joi.string().min(1).max(1000).required(),
  messageType: Joi.string().valid('text', 'image', 'file', 'location').default('text'),
  conversationId: Joi.string().required()
});

export const conversationCreateSchema = Joi.object({
  serviceRequestId: Joi.string().required(),
  providerId: Joi.string().required()
});

export const chatQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50)
}); 