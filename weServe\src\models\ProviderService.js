import mongoose from 'mongoose';

const serviceAvailabilitySchema = new mongoose.Schema({
  day: {
    type: String,
    required: true,
    enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  },
  startTime: {
    type: String,
    required: true,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ // Format HH:MM
  },
  endTime: {
    type: String,
    required: true,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ // Format HH:MM
  },
  available: {
    type: Boolean,
    default: true
  }
});

const providerServiceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  category: {
    type: String,
    required: true,
    enum: ['Plomberie', 'Électricité', 'Ménage', 'Jardinage', 'Déménagement', 'Peinture', 'Réparation', 'Installation', 'Autre']
  },
  photos: [{
    type: String // URLs Cloudinary
  }],
  price: {
    type: Number,
    required: true,
    min: 5,
    max: 2000
  },
  location: {
    address: {
      type: String,
      required: false
    },
    coordinates: {
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point'
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        index: '2dsphere'
      }
    }
  },
  availability: [serviceAvailabilitySchema],
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  reviews: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Review'
  }],
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  providerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index pour la recherche géospatiale
providerServiceSchema.index({ 'location.coordinates': '2dsphere' });

// Index pour la recherche par catégorie et statut
providerServiceSchema.index({ category: 1, status: 1 });

// Index pour la recherche par prestataire
providerServiceSchema.index({ providerId: 1 });

// Index pour la recherche par prix
providerServiceSchema.index({ price: 1 });

// Méthode pour calculer la note moyenne
providerServiceSchema.methods.calculateAverageRating = async function() {
  if (this.reviews.length === 0) {
    this.rating = 0;
    return;
  }

  const Review = mongoose.model('Review');
  const reviews = await Review.find({ _id: { $in: this.reviews } });
  
  if (reviews.length === 0) {
    this.rating = 0;
    return;
  }

  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  this.rating = Math.round((totalRating / reviews.length) * 10) / 10; // Arrondir à 1 décimale
};

// Middleware pre-save pour calculer la note moyenne
providerServiceSchema.pre('save', async function(next) {
  if (this.isModified('reviews')) {
    await this.calculateAverageRating();
  }
  next();
});

// Méthode statique pour rechercher des services par critères
providerServiceSchema.statics.searchByCriteria = function(criteria) {
  const { category, maxPrice, location, status = 'active' } = criteria;
  
  let query = { status };
  
  if (category) {
    query.category = category;
  }
  
  if (maxPrice) {
    query.price = { $lte: maxPrice };
  }
  
  if (location && location.latitude && location.longitude) {
    const radius = location.radius || 10; // Rayon par défaut en km
    query['location.coordinates'] = {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [location.longitude, location.latitude]
        },
        $maxDistance: radius * 1000 // Convertir en mètres
      }
    };
  }
  
  return this.find(query).populate('providerId', 'name email phone avatarUrl averageRating totalRatings');
};

// Méthode statique pour obtenir les services d'un prestataire
providerServiceSchema.statics.getByProvider = function(providerId, status = null) {
  let query = { providerId };
  
  if (status) {
    query.status = status;
  }
  
  return this.find(query).populate('providerId', 'name email phone avatarUrl averageRating totalRatings');
};

export default mongoose.model('ProviderService', providerServiceSchema);
