# 🚀 WeServe Backend - Résumé pour Frontend

## 📋 **Vue d'ensemble**
WeServe est une plateforme qui connecte les **clients** (qui demandent des services) avec les **prestataires** (qui offrent des services). L'API est sécurisée par JWT et utilise des cookies HttpOnly.

---

## 🔌 **Informations de Connexion**

### **URL de Base**
```
http://localhost:4000/api/v1
```

### **Port par Défaut**
- **Backend** : Port 4000
- **Frontend** : Port 3000 (configuré dans CORS)

### **Documentation API**
```
http://localhost:4000/api-docs
```

---

## 🔐 **Authentification**

### **Endpoints d'Auth**
| Méthode | Endpoint | Description | Rôle |
|---------|----------|-------------|------|
| `POST` | `/auth/signup` | Inscription utilisateur | Public |
| `POST` | `/auth/login` | Connexion utilisateur | Public |
| `GET` | `/auth/me` | Profil utilisateur actuel | Authentifié |
| `POST` | `/auth/refresh` | Rafraîchir le token | Authentifié |
| `POST` | `/auth/logout` | Déconnexion | Authentifié |

### **Structure d'Inscription**
```javascript
{
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "password": "string",
  "phone": "string", // optionnel
  "role": "client" | "provider"
}
```

### **Structure de Connexion**
```javascript
{
  "email": "string",
  "password": "string"
}
```

### **Réponse d'Auth**
```javascript
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "client" | "provider"
    },
    "tokens": {
      "accessToken": "string",
      "refreshToken": "string"
    }
  }
}
```

---

## 🛠️ **Demandes de Service (ServiceRequests)**

### **Endpoints**
| Méthode | Endpoint | Description | Rôle |
|---------|----------|-------------|------|
| `POST` | `/services` | Créer une demande | Client |
| `GET` | `/services` | Lister les demandes ouvertes | Prestataire |
| `GET` | `/services/:id` | Détail d'une demande | Authentifié |
| `PUT` | `/services/:id/status` | Changer le statut | Prestataire/Client |
| `GET` | `/services/my-services` | Mes demandes | Client |
| `GET` | `/services/search` | Rechercher des demandes | Authentifié |
| `GET` | `/services/nearby` | Demandes à proximité | Authentifié |
| `GET` | `/services/categories` | Liste des catégories | Authentifié |

### **Création d'une Demande (Client)**
```javascript
// POST /services
{
  "title": "Réparation de robinet",
  "description": "Mon robinet fuit depuis hier",
  "category": "Plomberie",
  "budget": 80,
  "date": "2024-12-15T10:00:00.000Z",
  "location": {
    "address": "123 Rue de la Paix, Paris",
    "latitude": 48.8566,
    "longitude": 2.3522
  }
}

// Photos : FormData multipart (max 5 photos, 10MB total)
```

### **Statuts des Demandes**
- `pending` : En attente
- `accepted` : Acceptée par un prestataire
- `in_progress` : En cours
- `completed` : Terminée
- `cancelled` : Annulée

---

## 🏪 **Services des Prestataires (ProviderServices)**

### **Endpoints**
| Méthode | Endpoint | Description | Rôle |
|---------|----------|-------------|------|
| `POST` | `/provider-services` | Créer un service | Prestataire |
| `GET` | `/provider-services` | Lister tous les services | Authentifié |
| `GET` | `/provider-services/:id` | Détail d'un service | Authentifié |
| `PUT` | `/provider-services/:id` | Modifier un service | Prestataire |
| `DELETE` | `/provider-services/:id` | Supprimer un service | Prestataire |
| `GET` | `/provider-services/my-services` | Mes services | Prestataire |

### **Création d'un Service (Prestataire)**
```javascript
// POST /provider-services
{
  "title": "Services de Plomberie",
  "description": "Plombier qualifié avec 10 ans d'expérience",
  "category": "Plomberie",
  "price": 65,
  "location": {
    "address": "Zone de Paris et banlieue",
    "latitude": 48.8566,
    "longitude": 2.3522
  },
  "availability": [
    {
      "day": "monday",
      "startTime": "08:00",
      "endTime": "18:00",
      "available": true
    }
    // ... autres jours
  ]
}

// Photos : FormData multipart (max 5 photos, 10MB total)
```

---

## 📊 **Tableau de Bord**

### **Endpoints**
| Méthode | Endpoint | Description | Rôle |
|---------|----------|-------------|------|
| `GET` | `/dashboard/requests` | Mes demandes | Client |
| `GET` | `/dashboard/completed` | Services terminés | Prestataire |

---

## ⭐ **Avis et Évaluations**

### **Endpoints**
| Méthode | Endpoint | Description | Rôle |
|---------|----------|-------------|------|
| `POST` | `/reviews` | Créer un avis | Client |
| `GET` | `/reviews` | Lister les avis | Authentifié |

### **Création d'un Avis**
```javascript
// POST /reviews
{
  "serviceRequestId": "string",
  "rating": 5, // 1-5
  "comment": "Excellent service, très professionnel"
}
```

---

## 🗂️ **Catégories de Services**

### **Catégories Disponibles**
```javascript
[
  "Plomberie",
  "Électricité", 
  "Ménage",
  "Jardinage",
  "Déménagement",
  "Peinture",
  "Réparation",
  "Installation",
  "Autre"
]
```

---

## 📍 **Géolocalisation**

### **Endpoints de Proximité**
- `/services/nearby` : Demandes à proximité
- `/provider-services` : Services à proximité (avec filtres)

### **Paramètres de Géolocalisation**
```javascript
{
  "latitude": "number",
  "longitude": "number", 
  "radius": "number" // en kilomètres (défaut: 10)
}
```

---

## 🔍 **Recherche et Filtres**

### **Recherche de Demandes**
```javascript
// GET /services/search
{
  "q": "plomberie",           // Recherche textuelle
  "category": "Plomberie",    // Filtre par catégorie
  "minBudget": 50,            // Budget minimum
  "maxBudget": 200            // Budget maximum
}
```

### **Filtres de Services**
```javascript
// GET /provider-services
{
  "category": "Plomberie",    // Filtre par catégorie
  "maxPrice": 100,            // Prix maximum
  "latitude": 48.8566,        // Localisation
  "longitude": 2.3522,
  "radius": 5                 // Rayon en km
}
```

---

## 📱 **Configuration Frontend**

### **Headers d'Authentification**
```javascript
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}` // JWT token
};
```

### **Gestion des Cookies**
```javascript
// Pour les navigateurs web
fetch(url, {
  credentials: 'include' // Inclure les cookies HttpOnly
});

// Pour React Native
// Utiliser AsyncStorage pour stocker le token
```

### **Gestion des Erreurs**
```javascript
// Codes d'erreur HTTP
400: Données invalides
401: Non authentifié  
403: Non autorisé
404: Ressource non trouvée
500: Erreur serveur
```

---

## 🚨 **Limites et Contraintes**

### **Photos**
- **Maximum** : 5 photos par service/demande
- **Taille totale** : 10MB maximum
- **Formats** : JPEG, PNG
- **Upload** : Multipart FormData

### **Validation**
- **Titre** : 100 caractères max
- **Description** : 1000 caractères max
- **Budget** : Nombre positif
- **Prix** : Nombre positif
- **Coordonnées** : Latitude (-90 à 90), Longitude (-180 à 180)

---

## 🔄 **Workflow de l'Application**

### **1. Création de Services (Prestataires)**
1. Prestataire crée un service via `POST /provider-services`
2. Service visible par tous les clients
3. Clients peuvent consulter et contacter

### **2. Création de Demandes (Clients)**
1. Client crée une demande via `POST /services`
2. Demande visible par tous les prestataires
3. Prestataires peuvent accepter

### **3. Gestion des Demandes**
1. Prestataire accepte une demande
2. Statut passe de `pending` à `accepted`
3. Prestataire met à jour le statut (`in_progress`, `completed`)

---

## 🧪 **Test et Développement**

### **Collection Postman**
- **Fichier** : `postman/WeServe.postman_collection.json`
- **Environnement** : `postman/WeServe.local.postman_environment.json`

### **Variables d'Environnement**
```bash
# .env
PORT=4000
CLIENT_ORIGIN=http://localhost:3000
MONGODB_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
```

### **Démarrage du Serveur**
```bash
npm run dev    # Développement avec nodemon
npm start      # Production
```

---

## 📚 **Ressources Supplémentaires**

- **Documentation complète** : `FRONTEND_API_DOCUMENTATION.md`
- **Guide d'implémentation** : `BACKEND_IMPLEMENTATION_GUIDE.md`
- **Tests** : `tests/` directory
- **Modèles de données** : `src/models/`

---

## 🎯 **Points Clés pour le Frontend**

1. **Port 4000** : Tous les appels API utilisent le port 4000
2. **Authentification JWT** : Token dans le header Authorization
3. **Rôles** : Distinction claire entre `client` et `provider`
4. **Photos** : Upload multipart avec FormData
5. **Géolocalisation** : Coordonnées requises pour les services
6. **Validation** : Respecter les limites de caractères et types
7. **Gestion d'erreurs** : Traiter tous les codes HTTP
8. **Responsive** : API supporte les filtres et la pagination

---

**🚀 Prêt à connecter votre frontend à WeServe !**


