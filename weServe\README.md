## WeServe Backend

Node.js + Express + MongoDB backend for connecting clients with local service providers.

### Tech Stack
- Express, Mongoose, JWT
- Passport (Google/Facebook)
- Multer + Cloudinary for image uploads
- Joi validation
- Swagger docs at `/api-docs`
- Jest for tests

### Auth tokens via HttpOnly cookies
- After signup/login/social/refresh, tokens are set as HttpOnly cookies: `accessToken` and `refreshToken`.
- <PERSON><PERSON> is configured with `credentials: true`. Set `CLIENT_ORIGIN` in `.env` to your frontend origin.
- In browsers, send `credentials: 'include'` with fetch/XHR.
- Logout clears cookies.

### Getting Started
1. Clone and enter the project
2. Copy `.env.example` to `.env` and fill values (set `CLIENT_ORIGIN`)
3. Install dependencies:
   - `npm install`
4. Run dev server:
   - `npm run dev`

### Scripts
- `npm run dev` - start with nodemon
- `npm start` - start
- `npm test` - run tests

### API
- Health: `GET /health`
- Docs: `GET /api-docs`

### Auth
- `POST /api/v1/auth/signup` - name, email+password or phone, role
- `POST /api/v1/auth/login` - email+password or phone
- `POST /api/v1/auth/refresh` - reads refresh token from cookie (or body) and rotates cookies
- `GET /api/v1/auth/google` and `/facebook` - OAuth flows
- `POST /api/v1/auth/password/request-reset` and `/password/reset`
- `POST /api/v1/auth/logout` - clears cookies

### Services
- `POST /api/v1/services` (client) - create request with up to 5 photos (field: photos[])
- `GET /api/v1/services` (provider) - list open requests
- `GET /api/v1/services/:id`
- `PUT /api/v1/services/:id/status` - change status

### Reviews
- `POST /api/v1/reviews` (client) - after completion

### Dashboard
- `GET /api/v1/dashboard/requests` (client)
- `GET /api/v1/dashboard/completed` (provider)

### Testing
- Unit tests live in `tests/` and mock Mongoose models.

### Notes
- OAuth routes require valid credentials. If not provided, they are skipped at runtime.
- Email/SMS senders are no-op when not configured. 