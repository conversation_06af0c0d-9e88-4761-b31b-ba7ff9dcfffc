import swaggerJsdoc from 'swagger-jsdoc';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'WeServe API',
      version: '1.0.0',
      description: 'API for connecting clients with local service providers.'
    },
    servers: [{ url: '/'}],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            email: { type: 'string' },
            phone: { type: 'string' },
            avatarUrl: { type: 'string' },
            role: { type: 'string', enum: ['client', 'provider'] }
          }
        },
        ServiceRequest: {
          type: 'object',
          properties: {
            title: { type: 'string' },
            description: { type: 'string' },
            photos: { type: 'array', items: { type: 'string' } },
            location: { type: 'object' },
            date: { type: 'string', format: 'date-time' },
            budget: { type: 'number' },
            status: { type: 'string', enum: ['pending', 'accepted', 'completed', 'cancelled'] }
          }
        },
        Review: {
          type: 'object',
          properties: {
            rating: { type: 'integer', minimum: 1, maximum: 5 },
            comment: { type: 'string' }
          }
        }
      }
    }
  },
  apis: ['src/routes/*.js']
};

export const swaggerSpec = swaggerJsdoc(options); 