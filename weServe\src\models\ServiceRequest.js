import mongoose from 'mongoose';

const serviceRequestSchema = new mongoose.Schema(
  {
    title: { 
      type: String, 
      required: true, 
      trim: true,
      minlength: 3,
      maxlength: 100
    },
    description: { 
      type: String, 
      required: true, 
      trim: true,
      minlength: 10,
      maxlength: 1000
    },
    category: { 
      type: String, 
      required: true,
      enum: ['Plomberie', 'Électricité', 'Ménage', 'Jardinage', 'Déménagement', 'Peinture', 'Réparation', 'Installation', 'Autre']
    },
    budget: { 
      type: Number, 
      required: true,
      min: 0
    },
    date: { 
      type: Date, 
      required: true,
      validate: {
        validator: function(value) {
          return value > new Date();
        },
        message: 'La date doit être dans le futur'
      }
    },
    location: {
      address: { 
        type: String, 
        required: true,
        trim: true
      },
      coordinates: {
        type: {
          type: String,
          enum: ['Point'],
          default: 'Point'
        },
        coordinates: {
          type: [Number], // [longitude, latitude]
          required: true,
          validate: {
            validator: function(coords) {
              return coords.length === 2 && 
                     coords[0] >= -180 && coords[0] <= 180 && // longitude
                     coords[1] >= -90 && coords[1] <= 90;     // latitude
            },
            message: 'Coordonnées invalides'
          }
        }
      }
    },
    photos: [{
      type: String, // base64 encoded images
      validate: {
        validator: function(photos) {
          return photos.length <= 5;
        },
        message: 'Maximum 5 photos autorisées'
      }
    }],
    clientId: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'User', 
      required: true 
    },
    status: { 
      type: String, 
      enum: ['pending', 'accepted', 'in_progress', 'completed', 'cancelled'],
      default: 'pending'
    },
    providers: [{
      providerId: { 
        type: mongoose.Schema.Types.ObjectId, 
        ref: 'User',
        required: true
      },
      acceptedAt: { 
        type: Date, 
        default: Date.now 
      },
      message: { 
        type: String, 
        trim: true,
        maxlength: 500
      }
    }],
    chosenProvider: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'User'
    },
    chosenAt: { 
      type: Date 
    },
    completedAt: { 
      type: Date 
    },
    cancelledAt: { 
      type: Date 
    },
    cancellationReason: { 
      type: String, 
      trim: true,
      maxlength: 500
    }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Index pour améliorer les performances
serviceRequestSchema.index({ status: 1, createdAt: -1 });
serviceRequestSchema.index({ clientId: 1, createdAt: -1 });
serviceRequestSchema.index({ 'providers.providerId': 1 });
serviceRequestSchema.index({ location: '2dsphere' });
serviceRequestSchema.index({ category: 1, status: 1 });

// Virtual pour vérifier si un provider peut encore accepter
serviceRequestSchema.virtual('canAcceptProviders').get(function() {
  return this.status === 'pending' && !this.chosenProvider;
});

// Virtual pour vérifier si la demande peut être choisie
serviceRequestSchema.virtual('canChooseProvider').get(function() {
  return this.status === 'pending' && this.providers.length > 0 && !this.chosenProvider;
});

// Méthode pour vérifier si un provider a déjà accepté
serviceRequestSchema.methods.hasProviderAccepted = function(providerId) {
  return this.providers.some(provider => 
    provider.providerId.toString() === providerId.toString()
  );
};

// Méthode pour ajouter un provider
serviceRequestSchema.methods.addProvider = function(providerId, message = '') {
  if (!this.canAcceptProviders) {
    throw new Error('Cette demande ne peut plus accepter de nouveaux prestataires');
  }
  
  if (this.hasProviderAccepted(providerId)) {
    throw new Error('Ce prestataire a déjà accepté cette demande');
  }
  
  this.providers.push({
    providerId,
    message,
    acceptedAt: new Date()
  });
  
  return this;
};

// Méthode pour choisir un provider
serviceRequestSchema.methods.chooseProvider = function(providerId) {
  if (!this.canChooseProvider) {
    throw new Error('Cette demande ne peut pas être choisie actuellement');
  }
  
  if (!this.hasProviderAccepted(providerId)) {
    throw new Error('Ce prestataire n\'a pas accepté cette demande');
  }
  
  this.chosenProvider = providerId;
  this.status = 'accepted';
  this.chosenAt = new Date();
  
  return this;
};

// Middleware pre-save pour validation
serviceRequestSchema.pre('save', function(next) {
  // Validation métier
  if (this.chosenProvider && this.status !== 'accepted') {
    this.status = 'accepted';
  }
  
  if (this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date();
  }
  
  if (this.status === 'cancelled' && !this.cancelledAt) {
    this.cancelledAt = new Date();
  }
  
  next();
});

const ServiceRequest = mongoose.model('ServiceRequest', serviceRequestSchema);
export default ServiceRequest; 