import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import User from '../models/User.js';
import { sendEmail } from '../utils/email.js';
import { sendSms } from '../utils/sms.js';

const ACCESS_EXPIRY = process.env.JWT_EXPIRES_IN || '15m';
const REFRESH_EXPIRY = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

function signAccessToken(userId) {
  return jwt.sign({ sub: userId }, process.env.JWT_SECRET || 'dev_secret', { expiresIn: ACCESS_EXPIRY });
}

function signRefreshToken(userId) {
  return jwt.sign({ sub: userId, type: 'refresh' }, process.env.JWT_REFRESH_SECRET || 'dev_refresh_secret', { expiresIn: REFRESH_EXPIRY });
}

export async function signup({ name, email, phone, password, role }) {
  const existing = await User.findOne({ $or: [email ? { email } : null, phone ? { phone } : null].filter(Boolean) });
  if (existing) throw Object.assign(new Error('User already exists'), { status: 400 });
  const passwordHash = password ? await bcrypt.hash(password, 10) : undefined;
  const user = await User.create({ name, email, phone, passwordHash, role, authProvider: password ? 'local' : 'local' });
  const accessToken = signAccessToken(user._id.toString());
  const refreshToken = signRefreshToken(user._id.toString());
  user.refreshToken = refreshToken;
  await user.save();
  return { user, accessToken, refreshToken };
}

export async function login({ email, phone, password }) {
  const user = await User.findOne({ $or: [email ? { email } : null, phone ? { phone } : null].filter(Boolean) });
  if (!user) throw Object.assign(new Error('Invalid credentials'), { status: 401 });
  if (email) {
    const ok = await bcrypt.compare(password, user.passwordHash || '');
    if (!ok) throw Object.assign(new Error('Invalid credentials'), { status: 401 });
  }
  const accessToken = signAccessToken(user._id.toString());
  const refreshToken = signRefreshToken(user._id.toString());
  user.refreshToken = refreshToken;
  await user.save();
  return { user, accessToken, refreshToken };
}

export async function socialLogin(user) {
  const accessToken = signAccessToken(user._id.toString());
  const refreshToken = signRefreshToken(user._id.toString());
  user.refreshToken = refreshToken;
  await user.save();
  return { user, accessToken, refreshToken };
}

export async function refresh(refreshToken) {
  try {
    const payload = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || 'dev_refresh_secret');
    const user = await User.findById(payload.sub);
    if (!user || user.refreshToken !== refreshToken) {
      throw Object.assign(new Error('Invalid refresh token'), { status: 401 });
    }
    const accessToken = signAccessToken(user._id.toString());
    const newRefreshToken = signRefreshToken(user._id.toString());
    user.refreshToken = newRefreshToken;
    await user.save();
    return { accessToken, refreshToken: newRefreshToken };
  } catch (err) {
    throw Object.assign(new Error('Invalid refresh token'), { status: 401 });
  }
}

export async function requestPasswordReset({ email, phone }) {
  const user = await User.findOne({ $or: [email ? { email } : null, phone ? { phone } : null].filter(Boolean) });
  if (!user) return; // do not reveal
  const token = crypto.randomBytes(20).toString('hex');
  user.resetPasswordToken = token;
  user.resetPasswordExpires = new Date(Date.now() + 60 * 60 * 1000);
  await user.save();
  if (email) {
    await sendEmail(email, 'Password Reset', `Your reset token is: ${token}`);
  } else if (phone) {
    await sendSms(phone, `Your reset token is: ${token}`);
  }
}

export async function resetPassword({ token, newPassword }) {
  const user = await User.findOne({ resetPasswordToken: token, resetPasswordExpires: { $gt: new Date() } });
  if (!user) throw Object.assign(new Error('Invalid or expired token'), { status: 400 });
  user.passwordHash = await bcrypt.hash(newPassword, 10);
  user.resetPasswordToken = undefined;
  user.resetPasswordExpires = undefined;
  await user.save();
} 