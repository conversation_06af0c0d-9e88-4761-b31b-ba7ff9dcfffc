import 'react-native-gesture-handler/jestSetup';

// Mock des modules natifs
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

jest.mock('expo-location');
jest.mock('expo-camera');
jest.mock('expo-image-picker');
jest.mock('expo-notifications');
jest.mock('react-native-maps');

// Mock d'AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock de react-native-vector-icons
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');

// Mock de react-native-linear-gradient
// LinearGradient mock supprimé

// Configuration globale pour les tests
global.console = {
  ...console,
  // Supprimer les warnings en mode test
  warn: jest.fn(),
  error: jest.fn(),
};
