export const COLORS = {
  primary: '#2563EB',      // Bleu principal
  secondary: '#10B981',    // Vert succès
  accent: '#F59E0B',       // Orange accent
  danger: '#EF4444',       // Rouge erreur
  warning: '#F97316',      // Orange avertissement
  success: '#22C55E',      // Vert succès
  dark: '#1F2937',         // Noir texte
  light: '#F9FAFB',        // <PERSON> fond
  gray: '#6B7280',         // Gris texte
  border: '#E5E7EB',       // Gris bordure
  white: '#FFFFFF',        // <PERSON> pur
  black: '#000000',        // Noir pur
  transparent: 'transparent'
};

export const SIZES = {
  base: 8,
  small: 12,
  font: 14,
  medium: 16,
  large: 18,
  extraLarge: 24,
  padding: 16,
  radius: 8,
  icon: 24,
  buttonHeight: 48
};

export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System'
};

export const SHADOWS = {
  small: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 4,
  },
  large: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 8,
  }
};
