import { create } from 'zustand';

export const useLocationStore = create((set, get) => ({
  currentLocation: null,
  selectedLocation: null,
  hasPermission: false,
  isLoading: false,
  error: null,
  
  setCurrentLocation: (location) => set({ currentLocation: location }),
  
  setSelectedLocation: (location) => set({ selectedLocation: location }),
  
  setPermission: (hasPermission) => set({ hasPermission }),
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  setError: (error) => set({ error, isLoading: false }),
  
  clearError: () => set({ error: null }),
  
  getDistance: (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Rayon de la Terre en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  },
  
  getLocationFromAddress: async (address) => {
    try {
      // Simulation d'une API de géocodage
      // En production, utilisez Google Maps Geocoding API ou similaire
      return {
        latitude: 48.8566,
        longitude: 2.3522,
        address: address
      };
    } catch (error) {
      console.error('Erreur de géocodage:', error);
      return null;
    }
  }
}));
