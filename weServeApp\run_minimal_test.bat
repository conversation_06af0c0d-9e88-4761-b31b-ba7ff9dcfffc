@echo off
echo ========================================
echo    Test Minimal JMeter pour weServeApp
echo ========================================
echo.

echo 🔍 Vérification de JMeter...
echo.

REM Essayer de trouver JMeter
set JMETER_FOUND=false

REM Vérifier dans le PATH
where jmeter >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ JMeter trouvé dans le PATH
    set JMETER_CMD=jmeter
    set JMETER_FOUND=true
    goto run_test
)

REM Emplacements courants
if exist "C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files
    set JMETER_FOUND=true
    goto run_test
)

if exist "C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files (x86)
    set JMETER_FOUND=true
    goto run_test
)

if exist "%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Downloads
    set JMETER_FOUND=true
    goto run_test
)

if exist "%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé sur le Bureau
    set JMETER_FOUND=true
    goto run_test
)

if exist "C:\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans C:\
    set JMETER_FOUND=true
    goto run_test
)

if "%JMETER_FOUND%"=="false" (
    echo ❌ JMeter non trouvé!
    echo.
    echo Veuillez:
    echo 1. Installer JMeter depuis: https://jmeter.apache.org/download_jmeter.cgi
    echo 2. Ou spécifier le chemin manuellement
    echo.
    set /p custom_path="Chemin complet vers jmeter.bat: "
    if exist "%custom_path%" (
        set JMETER_CMD="%custom_path%"
        echo ✅ Chemin personnalisé accepté
        set JMETER_FOUND=true
        goto run_test
    ) else (
        echo ❌ Fichier non trouvé
        pause
        exit /b 1
    )
)

:run_test
echo.
echo 🎯 Test Minimal avec: %JMETER_CMD%
echo.

echo 📋 Test de connexion à l'API weServeApp...
echo URL: http://localhost:5000/api/v1/services/categories
echo.

echo ⚠️  IMPORTANT: Assurez-vous que votre backend weServeApp est démarré!
echo    - Port: 5000
echo    - Endpoint: /api/v1/services/categories
echo.

set /p confirm="Votre backend est-il démarré? (o/n): "
if /i "%confirm%"=="o" (
    echo.
    echo 🚀 Démarrage du test minimal...
    echo.
    
    REM Créer le dossier de résultats s'il n'existe pas
    if not exist "jmeter_results" mkdir jmeter_results
    
    REM Exécuter le test minimal
    %JMETER_CMD% -n -t weServeApp_TestPlan_Minimal.jmx -l jmeter_results/minimal_test_results.jtl
    
    if %errorlevel% equ 0 (
        echo.
        echo ✅ Test minimal terminé avec succès!
        echo.
        echo 📊 Résultats disponibles dans: jmeter_results/minimal_test_results.jtl
        echo.
        echo 🎉 Félicitations! JMeter fonctionne correctement avec votre application.
        echo.
        echo 🚀 Vous pouvez maintenant utiliser:
        echo - run_simple_test.bat (pour des tests plus complets)
        echo - run_jmeter_tests.bat (pour tous les types de tests)
        echo.
    ) else (
        echo.
        echo ❌ Erreur lors de l'exécution du test
        echo.
        echo 🔍 Vérifiez:
        echo 1. Que votre backend weServeApp est démarré sur le port 5000
        echo 2. Que l'endpoint /api/v1/services/categories est accessible
        echo 3. Que CORS est configuré correctement
        echo.
        echo 💡 Testez d'abord avec votre navigateur:
        echo http://localhost:5000/api/v1/services/categories
        echo.
    )
) else (
    echo.
    echo ⚠️  Veuillez démarrer votre backend weServeApp d'abord
    echo.
    echo 📋 Instructions:
    echo 1. Ouvrez un terminal dans le dossier de votre backend
    echo 2. Exécutez: npm start ou npm run dev
    echo 3. Vérifiez que le serveur démarre sur le port 5000
    echo 4. Relancez ce script
    echo.
)

echo Appuyez sur une touche pour continuer...
pause >nul 