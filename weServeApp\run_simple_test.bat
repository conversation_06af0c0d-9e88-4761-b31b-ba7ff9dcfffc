@echo off
echo ========================================
echo    Test Simple JMeter pour weServeApp
echo ========================================
echo.

echo 🔍 Vérification de JMeter...
echo.

REM Essayer de trouver JMeter
set JMETER_FOUND=false

REM Vérifier dans le PATH
where jmeter >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ JMeter trouvé dans le PATH
    set JMETER_CMD=jmeter
    set JMETER_FOUND=true
    goto run_test
)

REM Emplacements courants
if exist "C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files
    set JMETER_FOUND=true
    goto run_test
)

if exist "C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\Program Files (x86)\Apache Software Foundation\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Program Files (x86)
    set JMETER_FOUND=true
    goto run_test
)

if exist "%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Downloads\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans Downloads
    set JMETER_FOUND=true
    goto run_test
)

if exist "%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="%USERPROFILE%\Desktop\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé sur le Bureau
    set JMETER_FOUND=true
    goto run_test
)

if exist "C:\apache-jmeter-5.6.3\bin\jmeter.bat" (
    set JMETER_CMD="C:\apache-jmeter-5.6.3\bin\jmeter.bat"
    echo ✅ JMeter trouvé dans C:\
    set JMETER_FOUND=true
    goto run_test
)

if "%JMETER_FOUND%"=="false" (
    echo ❌ JMeter non trouvé!
    echo.
    echo Veuillez:
    echo 1. Installer JMeter depuis: https://jmeter.apache.org/download_jmeter.cgi
    echo 2. Ou spécifier le chemin manuellement
    echo.
    set /p custom_path="Chemin complet vers jmeter.bat: "
    if exist "%custom_path%" (
        set JMETER_CMD="%custom_path%"
        echo ✅ Chemin personnalisé accepté
        set JMETER_FOUND=true
        goto run_test
    ) else (
        echo ❌ Fichier non trouvé
        pause
        exit /b 1
    )
)

:run_test
echo.
echo 🎯 Test Simple avec: %JMETER_CMD%
echo.

echo 📋 Vérification que le backend est accessible...
echo Test de connexion à http://localhost:4000/api/v1/services/categories
echo.

echo 🚀 Démarrage du test simple...
echo.

REM Créer le dossier de résultats s'il n'existe pas
if not exist "jmeter_results" mkdir jmeter_results

REM Exécuter le test simple
%JMETER_CMD% -n -t weServeApp_TestPlan_Simple.jmx -l jmeter_results/simple_test_results.jtl -e -o jmeter_results/simple_test_report

if %errorlevel% equ 0 (
    echo.
    echo ✅ Test simple terminé avec succès!
    echo.
    echo 📊 Résultats disponibles dans: jmeter_results/simple_test_report/
    echo 📝 Logs disponibles dans: jmeter_results/simple_test_results.jtl
    echo.
    echo 🌐 Ouvrez jmeter_results/simple_test_report/index.html dans votre navigateur
    echo pour voir les résultats détaillés.
    echo.
) else (
    echo.
    echo ❌ Erreur lors de l'exécution du test
    echo Vérifiez que votre backend weServeApp est démarré sur le port 4000
    echo.
)

echo Appuyez sur une touche pour continuer...
pause >nul 