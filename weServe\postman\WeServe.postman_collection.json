{"info": {"name": "WeServe API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Use <PERSON><PERSON>'s cookie jar. After login/signup/refresh, the server sets HttpOnly cookies."}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000"}, {"key": "serviceRequestId", "value": ""}, {"key": "providerId", "value": ""}, {"key": "userId", "value": ""}, {"key": "conversationSid", "value": ""}], "item": [{"name": "Health", "item": [{"name": "GET /health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}, {"name": "<PERSON><PERSON> (Client)", "item": [{"name": "POST /auth/signup (email)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/signup", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "signup"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Client User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"secret123\",\n  \"role\": \"client\"\n}"}, "description": "Cookies are set by the server (HttpOnly). Enable Postman cookie jar."}, "event": [{"listen": "test", "script": {"exec": ["console.log('Response:', pm.response.text());", "const data = pm.response.json && pm.response.json() || {};", "if (data.user && (data.user.id || data.user._id)) {", "  pm.environment.set('userId', data.user.id || data.user._id);", "  console.log('User ID set:', data.user.id || data.user._id);", "} else {", "  console.log('No user ID in response:', data);", "}"]}}]}, {"name": "POST /auth/login (email)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"secret123\"\n}"}, "description": "Relies on cookie jar for auth."}, "event": [{"listen": "test", "script": {"exec": ["console.log('Response:', pm.response.text());", "const data = pm.response.json && pm.response.json() || {};", "if (data.user && (data.user.id || data.user._id)) {", "  pm.environment.set('userId', data.user.id || data.user._id);", "  console.log('User ID set:', data.user.id || data.user._id);", "} else {", "  console.log('No user ID in response:', data);", "}"]}}]}, {"name": "POST /auth/refresh", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}, "body": {"mode": "raw", "raw": "{}"}, "description": "Reads refreshToken from cookie and rotates cookies."}}, {"name": "POST /auth/logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "logout"]}}}]}, {"name": "<PERSON><PERSON> (Provider)", "item": [{"name": "POST /auth/signup (provider)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/signup", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "signup"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Provider User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"secret123\",\n  \"role\": \"provider\"\n}"}}}, {"name": "POST /auth/login (provider)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"secret123\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["console.log('Response:', pm.response.text());", "const data = pm.response.json && pm.response.json() || {};", "if (data.user && (data.user.id || data.user._id)) {", "  pm.environment.set('providerId', data.user.id || data.user._id);", "  console.log('Provider ID set:', data.user.id || data.user._id);", "} else {", "  console.log('No provider ID in response:', data);", "}"]}}]}]}, {"name": "Services", "item": [{"name": "POST /services (create, client)", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/services", "host": ["{{baseUrl}}"], "path": ["api", "v1", "services"]}, "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Fix kitchen sink", "type": "text"}, {"key": "description", "value": "Leaking faucet needs replacement.", "type": "text"}, {"key": "category", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"key": "budget", "value": "120", "type": "text"}, {"key": "date", "value": "2025-12-31T10:00:00.000Z", "type": "text"}, {"key": "location[address]", "value": "123 Main St, City", "type": "text"}, {"key": "location[longitude]", "value": "-122.4194", "type": "text"}, {"key": "location[latitude]", "value": "37.7749", "type": "text"}]}, "description": "Requires cookie from client login."}, "event": [{"listen": "test", "script": {"exec": ["console.log('Service Response:', pm.response.text());", "const data = pm.response.json && pm.response.json() || {};", "if (data.data && data.data.id) {", "  pm.environment.set('serviceRequestId', data.data.id);", "  console.log('Service ID set:', data.data.id);", "} else {", "  console.log('No service ID in response:', data);", "}"]}}]}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "POST /chat/create-conversation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/chat/create-conversation", "host": ["{{baseUrl}}"], "path": ["chat", "create-conversation"]}, "body": {"mode": "raw", "raw": "{\n  \"serviceRequestId\": \"{{serviceRequestId}}\",\n  \"providerId\": \"{{providerId}}\",\n  \"providerName\": \"Test Provider\"\n}"}, "description": "Create a new <PERSON><PERSON><PERSON> conversation. Requires authentication cookie."}, "event": [{"listen": "test", "script": {"exec": ["console.log('Conversation Response:', pm.response.text());", "const data = pm.response.json && pm.response.json() || {};", "if (data.conversationSid) {", "  pm.environment.set('conversationSid', data.conversationSid);", "  console.log('Conversation SID set:', data.conversationSid);", "} else {", "  console.log('No conversation SID in response:', data);", "}"]}}]}, {"name": "POST /chat/add-participant", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/chat/add-participant", "host": ["{{baseUrl}}"], "path": ["chat", "add-participant"]}, "body": {"mode": "raw", "raw": "{\n  \"identity\": \"{{userId}}\",\n  \"conversationSid\": \"{{conversationSid}}\",\n  \"participantType\": \"client\"\n}"}, "description": "Add participant to conversation. Requires authentication cookie."}}, {"name": "POST /chat/add-participant (provider)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/chat/add-participant", "host": ["{{baseUrl}}"], "path": ["chat", "add-participant"]}, "body": {"mode": "raw", "raw": "{\n  \"identity\": \"{{providerId}}\",\n  \"conversationSid\": \"{{conversationSid}}\",\n  \"participantType\": \"provider\"\n}"}, "description": "Add provider participant to conversation. Requires provider authentication cookie."}}, {"name": "POST /chat/send-message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/chat/send-message", "host": ["{{baseUrl}}"], "path": ["chat", "send-message"]}, "body": {"mode": "raw", "raw": "{\n  \"conversationSid\": \"{{conversationSid}}\",\n  \"body\": \"Hello! I'm interested in your service.\",\n  \"messageType\": \"text\"\n}"}, "description": "Send message in conversation. Requires authentication cookie."}}, {"name": "POST /chat/send-message (provider)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/chat/send-message", "host": ["{{baseUrl}}"], "path": ["chat", "send-message"]}, "body": {"mode": "raw", "raw": "{\n  \"conversationSid\": \"{{conversationSid}}\",\n  \"body\": \"Hi! I can help you with that. When would you like me to come?\",\n  \"messageType\": \"text\"\n}"}, "description": "Send provider message in conversation. Requires provider authentication cookie."}}, {"name": "GET /chat/messages/:conversationSid", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/chat/messages/{{conversationSid}}", "host": ["{{baseUrl}}"], "path": ["chat", "messages", "{{conversationSid}}"]}, "description": "Get messages from conversation. Requires authentication cookie."}}, {"name": "GET /chat/conversations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/chat/conversations", "host": ["{{baseUrl}}"], "path": ["chat", "conversations"]}, "description": "List user conversations. Requires authentication cookie."}}]}]}